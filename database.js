/**
 * 🗄️ VEGAS ACE SLOTS - PROFESSIONAL DATABASE SYSTEM 🗄️
 * Secure user data management with device tracking and analytics
 * Replaces browser localStorage with proper data persistence
 */

class VegasAceDatabase {
    constructor() {
        this.dbName = 'VegasAceSlots';
        this.version = 1;
        this.db = null;
        this.isInitialized = false;
        
        // 🔐 Security settings
        this.maxLoginAttempts = 5;
        this.sessionTimeout = 24 * 60 * 60 * 1000; // 24 hours
        this.deviceTrackingEnabled = true;
        
        console.log('🗄️ VegasAce Database System Initialized');
    }

    /**
     * 🚀 Initialize the database
     */
    async init() {
        try {
            await this.openDatabase();
            await this.createTables();
            this.isInitialized = true;
            console.log('✅ Database initialized successfully');
            return true;
        } catch (error) {
            console.error('❌ Database initialization failed:', error);
            return false;
        }
    }

    /**
     * 📂 Open IndexedDB database
     */
    openDatabase() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                this.db = request.result;
                resolve(this.db);
            };
            
            request.onupgradeneeded = (event) => {
                this.db = event.target.result;
                this.createObjectStores();
            };
        });
    }

    /**
     * 🏗️ Create database tables/object stores
     */
    createObjectStores() {
        // Users table
        if (!this.db.objectStoreNames.contains('users')) {
            const userStore = this.db.createObjectStore('users', { keyPath: 'id' });
            userStore.createIndex('mobile', 'mobile', { unique: true });
            userStore.createIndex('email', 'email', { unique: false });
        }

        // Sessions table
        if (!this.db.objectStoreNames.contains('sessions')) {
            const sessionStore = this.db.createObjectStore('sessions', { keyPath: 'sessionId' });
            sessionStore.createIndex('userId', 'userId', { unique: false });
            sessionStore.createIndex('deviceId', 'deviceId', { unique: false });
        }

        // Transactions table
        if (!this.db.objectStoreNames.contains('transactions')) {
            const transactionStore = this.db.createObjectStore('transactions', { keyPath: 'id' });
            transactionStore.createIndex('userId', 'userId', { unique: false });
            transactionStore.createIndex('type', 'type', { unique: false });
            transactionStore.createIndex('timestamp', 'timestamp', { unique: false });
        }

        // Game sessions table
        if (!this.db.objectStoreNames.contains('gameSessions')) {
            const gameStore = this.db.createObjectStore('gameSessions', { keyPath: 'id' });
            gameStore.createIndex('userId', 'userId', { unique: false });
            gameStore.createIndex('startTime', 'startTime', { unique: false });
        }

        // Device tracking table
        if (!this.db.objectStoreNames.contains('devices')) {
            const deviceStore = this.db.createObjectStore('devices', { keyPath: 'deviceId' });
            deviceStore.createIndex('userId', 'userId', { unique: false });
            deviceStore.createIndex('lastSeen', 'lastSeen', { unique: false });
        }

        console.log('🏗️ Database tables created');
    }

    /**
     * 🔐 Generate unique device fingerprint
     */
    generateDeviceFingerprint() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Device fingerprint', 2, 2);
        
        const fingerprint = {
            screen: `${screen.width}x${screen.height}`,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            language: navigator.language,
            platform: navigator.platform,
            userAgent: navigator.userAgent.substring(0, 100),
            canvas: canvas.toDataURL().substring(0, 50),
            timestamp: Date.now()
        };
        
        return btoa(JSON.stringify(fingerprint)).substring(0, 32);
    }

    /**
     * 👤 Create new user account
     */
    async createUser(userData) {
        try {
            const userId = 'VA' + Date.now().toString().slice(-8);
            const deviceId = this.generateDeviceFingerprint();
            
            const user = {
                id: userId,
                name: userData.name,
                mobile: userData.mobile,
                email: userData.email || '',
                password: await this.hashPassword(userData.password),
                balance: 1000.00, // Starting balance
                joinDate: new Date().toISOString(),
                lastLogin: new Date().toISOString(),
                isActive: true,
                isVerified: false,
                loginAttempts: 0,
                
                // Game statistics
                stats: {
                    totalDeposits: 1000.00,
                    totalWithdrawals: 0.00,
                    totalWagered: 0.00,
                    totalWon: 0.00,
                    gamesPlayed: 0,
                    winRate: 0,
                    biggestWin: 0,
                    longestSession: 0,
                    favoriteGame: 'slots'
                },
                
                // Security & tracking
                security: {
                    primaryDeviceId: deviceId,
                    allowedDevices: [deviceId],
                    lastPasswordChange: new Date().toISOString(),
                    twoFactorEnabled: false,
                    securityQuestions: []
                },
                
                // Preferences
                preferences: {
                    notifications: true,
                    soundEnabled: true,
                    autoPlay: false,
                    language: 'en',
                    currency: 'USD'
                }
            };

            await this.saveUser(user);
            await this.createInitialTransaction(userId);
            
            console.log('✅ User created successfully:', userId);
            return user;
            
        } catch (error) {
            console.error('❌ Error creating user:', error);
            throw error;
        }
    }

    /**
     * 🔒 Hash password securely
     */
    async hashPassword(password) {
        const encoder = new TextEncoder();
        const data = encoder.encode(password + 'VegasAceSalt2024');
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    }

    /**
     * 💾 Save user to database
     */
    async saveUser(user) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['users'], 'readwrite');
            const store = transaction.objectStore('users');
            const request = store.put(user);
            
            request.onsuccess = () => resolve(user);
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 🔍 Find user by mobile number
     */
    async findUserByMobile(mobile) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['users'], 'readonly');
            const store = transaction.objectStore('users');
            const index = store.index('mobile');
            const request = index.get(mobile);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 🔐 Authenticate user login
     */
    async authenticateUser(mobile, password) {
        try {
            const user = await this.findUserByMobile(mobile);
            if (!user) {
                console.log('❌ User not found:', mobile);
                return null;
            }

            if (!user.isActive) {
                console.log('❌ User account is deactivated:', mobile);
                return null;
            }

            if (user.loginAttempts >= this.maxLoginAttempts) {
                console.log('❌ Too many login attempts:', mobile);
                return null;
            }

            const hashedPassword = await this.hashPassword(password);
            if (user.password !== hashedPassword) {
                // Increment login attempts
                user.loginAttempts = (user.loginAttempts || 0) + 1;
                await this.saveUser(user);
                console.log('❌ Invalid password for:', mobile);
                return null;
            }

            // Check device security
            const deviceId = this.generateDeviceFingerprint();
            if (this.deviceTrackingEnabled && !user.security.allowedDevices.includes(deviceId)) {
                console.log('❌ Unauthorized device for:', mobile);
                return { error: 'unauthorized_device', user: user };
            }

            // Reset login attempts and update last login
            user.loginAttempts = 0;
            user.lastLogin = new Date().toISOString();
            await this.saveUser(user);

            // Create session
            await this.createSession(user.id, deviceId);

            console.log('✅ User authenticated successfully:', mobile);
            return user;

        } catch (error) {
            console.error('❌ Authentication error:', error);
            return null;
        }
    }

    /**
     * 🎮 Create game session
     */
    async createSession(userId, deviceId) {
        const session = {
            sessionId: 'SES' + Date.now(),
            userId: userId,
            deviceId: deviceId,
            startTime: new Date().toISOString(),
            lastActivity: new Date().toISOString(),
            isActive: true,
            gameData: {
                spinsPlayed: 0,
                totalWagered: 0,
                totalWon: 0,
                biggestWin: 0,
                bonusTriggered: 0
            }
        };

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['sessions'], 'readwrite');
            const store = transaction.objectStore('sessions');
            const request = store.add(session);
            
            request.onsuccess = () => resolve(session);
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 💰 Create initial welcome transaction
     */
    async createInitialTransaction(userId) {
        const transaction = {
            id: 'TXN' + Date.now(),
            userId: userId,
            type: 'deposit',
            amount: 1000.00,
            method: 'Welcome Bonus',
            status: 'completed',
            timestamp: new Date().toISOString(),
            description: 'Welcome bonus for new account',
            balanceAfter: 1000.00
        };

        return this.saveTransaction(transaction);
    }

    /**
     * 💾 Save transaction
     */
    async saveTransaction(transaction) {
        return new Promise((resolve, reject) => {
            const dbTransaction = this.db.transaction(['transactions'], 'readwrite');
            const store = dbTransaction.objectStore('transactions');
            const request = store.add(transaction);
            
            request.onsuccess = () => resolve(transaction);
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 📊 Get user transactions
     */
    async getUserTransactions(userId, limit = 50) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['transactions'], 'readonly');
            const store = transaction.objectStore('transactions');
            const index = store.index('userId');
            const request = index.getAll(userId);
            
            request.onsuccess = () => {
                const transactions = request.result
                    .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
                    .slice(0, limit);
                resolve(transactions);
            };
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 🔄 Update user balance
     */
    async updateUserBalance(userId, newBalance) {
        try {
            const user = await this.getUserById(userId);
            if (user) {
                user.balance = newBalance;
                await this.saveUser(user);
                return true;
            }
            return false;
        } catch (error) {
            console.error('❌ Error updating balance:', error);
            return false;
        }
    }

    /**
     * 👤 Get user by ID
     */
    async getUserById(userId) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['users'], 'readonly');
            const store = transaction.objectStore('users');
            const request = store.get(userId);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 🧹 Cleanup old sessions
     */
    async cleanupOldSessions() {
        const cutoffTime = Date.now() - this.sessionTimeout;
        // Implementation for cleaning up old sessions
        console.log('🧹 Cleaning up sessions older than', new Date(cutoffTime));
    }

    /**
     * 📈 Get analytics data
     */
    async getAnalytics(userId) {
        try {
            const user = await this.getUserById(userId);
            const transactions = await this.getUserTransactions(userId);
            
            return {
                user: user,
                transactions: transactions,
                summary: {
                    totalDeposits: user.stats.totalDeposits,
                    totalWithdrawals: user.stats.totalWithdrawals,
                    netPosition: user.stats.totalDeposits - user.stats.totalWithdrawals,
                    gamesPlayed: user.stats.gamesPlayed,
                    winRate: user.stats.winRate
                }
            };
        } catch (error) {
            console.error('❌ Error getting analytics:', error);
            return null;
        }
    }
}

// 🌟 Export the database class
window.VegasAceDatabase = VegasAceDatabase;

console.log('🗄️ VegasAce Database System Loaded');
