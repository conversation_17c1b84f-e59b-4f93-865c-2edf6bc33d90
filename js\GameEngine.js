/**
 * GameEngine.js - Core game logic and state management
 * Handles game flow, betting, and overall game state
 */

class GameEngine {
    /**
     * Initialize the game engine
     * @param {Phaser.Scene} scene - The Phaser scene
     * @param {Object} config - Game configuration
     * @param {Object} gameState - Game state object
     */
    constructor(scene, config, gameState) {
        this.scene = scene;
        this.config = config;
        this.gameState = gameState;

        // Betting configuration
        this.betLevels = [0.10, 0.25, 0.50, 1.00, 2.00, 5.00, 10.00, 25.00, 50.00, 100.00];
        this.currentBetIndex = 3; // Start at $1.00

        // Game statistics
        this.stats = {
            totalSpins: 0,
            totalWins: 0,
            biggestWin: 0,
            freeSpinsTriggered: 0,
            totalWinAmount: 0
        };

        this.initialize();
    }

    /**
     * Initialize the game engine
     */
    initialize() {
        this.setupEventListeners();
        this.updateGameState();
    }

    /**
     * Set up event listeners for game controls
     */
    setupEventListeners() {
        // Spin button
        const spinBtn = document.getElementById('spin-btn');
        spinBtn.addEventListener('click', () => this.handleSpin());

        // Bet controls
        const betMinus = document.getElementById('bet-minus');
        const betPlus = document.getElementById('bet-plus');
        const maxBetBtn = document.getElementById('max-bet-btn');

        betMinus.addEventListener('click', () => this.decreaseBet());
        betPlus.addEventListener('click', () => this.increaseBet());
        maxBetBtn.addEventListener('click', () => this.setMaxBet());

        // Keyboard controls
        document.addEventListener('keydown', (event) => this.handleKeyboard(event));
    }

    /**
     * Handle spin button click
     */
    async handleSpin() {
        if (this.gameState.isSpinning) return;

        // Validate bet
        if (!this.validateBet()) return;

        // Update statistics
        this.stats.totalSpins++;

        // Trigger spin in the main scene
        await this.scene.spin();

        // Update game state after spin
        this.updateGameState();
    }

    /**
     * Validate if the current bet is valid
     * @returns {boolean} True if bet is valid
     */
    validateBet() {
        if (this.gameState.inFreeSpins) return true;

        if (this.gameState.balance < this.gameState.currentBet) {
            this.showMessage('Insufficient balance for this bet!');
            return false;
        }

        return true;
    }

    /**
     * Decrease bet amount
     */
    decreaseBet() {
        if (this.gameState.isSpinning || this.gameState.inFreeSpins) return;

        if (this.currentBetIndex > 0) {
            this.currentBetIndex--;
            this.gameState.currentBet = this.betLevels[this.currentBetIndex];
            this.updateBetDisplay();
        }
    }

    /**
     * Increase bet amount
     */
    increaseBet() {
        if (this.gameState.isSpinning || this.gameState.inFreeSpins) return;

        if (this.currentBetIndex < this.betLevels.length - 1) {
            this.currentBetIndex++;
            this.gameState.currentBet = this.betLevels[this.currentBetIndex];
            this.updateBetDisplay();
        }
    }

    /**
     * Set maximum bet
     */
    setMaxBet() {
        if (this.gameState.isSpinning || this.gameState.inFreeSpins) return;

        // Find the highest bet the player can afford
        let maxAffordableIndex = 0;
        for (let i = this.betLevels.length - 1; i >= 0; i--) {
            if (this.gameState.balance >= this.betLevels[i]) {
                maxAffordableIndex = i;
                break;
            }
        }

        this.currentBetIndex = maxAffordableIndex;
        this.gameState.currentBet = this.betLevels[this.currentBetIndex];
        this.updateBetDisplay();
    }

    /**
     * Handle keyboard input
     * @param {KeyboardEvent} event - Keyboard event
     */
    handleKeyboard(event) {
        switch (event.code) {
            case 'Space':
                event.preventDefault();
                this.handleSpin();
                break;
            case 'ArrowUp':
                event.preventDefault();
                this.increaseBet();
                break;
            case 'ArrowDown':
                event.preventDefault();
                this.decreaseBet();
                break;
            case 'KeyM':
                event.preventDefault();
                this.setMaxBet();
                break;
        }
    }

    /**
     * Update bet display in UI
     */
    updateBetDisplay() {
        const betDisplay = document.getElementById('bet-display');
        const betAmount = document.getElementById('bet-amount');

        if (betDisplay) {
            betDisplay.textContent = `$${this.gameState.currentBet.toFixed(2)}`;
        }
        if (betAmount) {
            betAmount.textContent = `$${this.gameState.currentBet.toFixed(2)}`;
        }
    }

    /**
     * Update game state and UI
     */
    updateGameState() {
        this.updateBetDisplay();
        this.updateSpinButtonState();
        this.checkAutoplay();
    }

    /**
     * Update spin button state
     */
    updateSpinButtonState() {
        const spinBtn = document.getElementById('spin-btn');
        const spinText = spinBtn.querySelector('.spin-text');

        if (this.gameState.isSpinning) {
            spinBtn.disabled = true;
            spinText.textContent = 'SPINNING...';
        } else if (this.gameState.inFreeSpins) {
            spinBtn.disabled = false;
            spinText.textContent = `FREE SPIN (${this.gameState.freeSpinsRemaining})`;
        } else {
            spinBtn.disabled = this.gameState.balance < this.gameState.currentBet;
            spinText.textContent = 'SPIN';
        }
    }

    /**
     * Check for autoplay conditions
     */
    checkAutoplay() {
        // Placeholder for future autoplay feature
        // Could implement auto-spin functionality here
    }

    /**
     * Add winnings to balance and update statistics
     * @param {number} amount - Win amount
     */
    addWinnings(amount) {
        this.gameState.balance += amount;
        this.gameState.totalWin = amount;

        // Update statistics
        this.stats.totalWins++;
        this.stats.totalWinAmount += amount;

        if (amount > this.stats.biggestWin) {
            this.stats.biggestWin = amount;
        }

        // Check for big win celebration
        const winMultiplier = amount / this.gameState.currentBet;
        if (winMultiplier >= 10) {
            this.triggerBigWinCelebration(amount, winMultiplier);
        }
    }

    /**
     * Trigger big win celebration
     * @param {number} amount - Win amount
     * @param {number} multiplier - Win multiplier
     */
    triggerBigWinCelebration(amount, multiplier) {
        const celebration = document.getElementById('win-celebration');
        const winAmountBig = document.getElementById('win-amount-big');
        const winText = celebration.querySelector('.win-text');

        winAmountBig.textContent = `$${amount.toFixed(2)}`;

        if (multiplier >= 50) {
            winText.textContent = 'MEGA WIN!';
        } else if (multiplier >= 25) {
            winText.textContent = 'SUPER WIN!';
        } else {
            winText.textContent = 'BIG WIN!';
        }

        celebration.classList.remove('hidden');

        setTimeout(() => {
            celebration.classList.add('hidden');
        }, 3000);
    }

    /**
     * Start free spins mode
     * @param {number} count - Number of free spins
     */
    startFreeSpins(count) {
        this.gameState.inFreeSpins = true;
        this.gameState.freeSpinsRemaining = count;
        this.stats.freeSpinsTriggered++;

        // Show free spins display
        const freeSpinsDisplay = document.getElementById('freespins-display');
        const freeSpinsValue = document.getElementById('freespins-value');

        freeSpinsValue.textContent = count;
        freeSpinsDisplay.classList.remove('hidden');

        this.updateGameState();
        this.showMessage(`${count} FREE SPINS AWARDED!`);
    }

    /**
     * Add additional free spins
     * @param {number} count - Number of additional free spins
     */
    addFreeSpins(count) {
        this.gameState.freeSpinsRemaining += count;

        const freeSpinsValue = document.getElementById('freespins-value');
        freeSpinsValue.textContent = this.gameState.freeSpinsRemaining;

        this.showMessage(`+${count} FREE SPINS!`);
    }

    /**
     * End free spins mode
     */
    endFreeSpins() {
        this.gameState.inFreeSpins = false;
        this.gameState.freeSpinsRemaining = 0;

        // Hide free spins display
        const freeSpinsDisplay = document.getElementById('freespins-display');
        freeSpinsDisplay.classList.add('hidden');

        this.updateGameState();
    }

    /**
     * Show message to player
     * @param {string} message - Message to display
     */
    showMessage(message) {
        // Create temporary message display
        const messageDiv = document.createElement('div');
        messageDiv.className = 'game-message';
        messageDiv.textContent = message;
        messageDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: #ffd700;
            padding: 20px 40px;
            border-radius: 10px;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            font-size: 1.2rem;
            z-index: 1500;
            border: 2px solid #ffd700;
            animation: fadeInOut 2s ease-in-out;
        `;

        document.body.appendChild(messageDiv);

        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 2000);
    }

    /**
     * Get current game statistics
     * @returns {Object} Game statistics
     */
    getStatistics() {
        return {
            ...this.stats,
            currentBalance: this.gameState.balance,
            currentBet: this.gameState.currentBet,
            rtp: this.stats.totalSpins > 0 ? (this.stats.totalWinAmount / (this.stats.totalSpins * this.gameState.currentBet)) * 100 : 0
        };
    }

    /**
     * Reset game to initial state
     */
    resetGame() {
        this.gameState.balance = 1000.00;
        this.gameState.currentBet = 1.00;
        this.gameState.isSpinning = false;
        this.gameState.inFreeSpins = false;
        this.gameState.freeSpinsRemaining = 0;
        this.gameState.currentMultiplier = 1;
        this.gameState.totalWin = 0;

        this.currentBetIndex = 3;

        // Reset statistics
        this.stats = {
            totalSpins: 0,
            totalWins: 0,
            biggestWin: 0,
            freeSpinsTriggered: 0,
            totalWinAmount: 0
        };

        this.updateGameState();
    }

    /**
     * Save game state to localStorage
     */
    saveGame() {
        const saveData = {
            gameState: this.gameState,
            stats: this.stats,
            currentBetIndex: this.currentBetIndex
        };

        localStorage.setItem('vegasAceSlots_save', JSON.stringify(saveData));
    }

    /**
     * Load game state from localStorage
     */
    loadGame() {
        const saveData = localStorage.getItem('vegasAceSlots_save');

        if (saveData) {
            try {
                const data = JSON.parse(saveData);

                Object.assign(this.gameState, data.gameState);
                Object.assign(this.stats, data.stats);
                this.currentBetIndex = data.currentBetIndex || 3;

                this.updateGameState();
                return true;
            } catch (error) {
                console.error('Failed to load game:', error);
                return false;
            }
        }

        return false;
    }
}
