# Vegas Ace Slots - HTML5 Slot Game

A fully-featured Vegas-style slot game built with HTML5, CSS3, and Phaser.js featuring 1,024 ways to win, cascading reels, and exciting bonus features.

## 🎰 Game Features

### Core Gameplay
- **5x4 Grid Layout**: 5 reels with 4 rows each
- **1,024 Ways to Win**: Left-to-right adjacent symbol matching
- **Cascading Reels**: Winning symbols disappear, new ones fall down
- **97% RTP**: Mathematically balanced for fair gameplay
- **Mobile-First Design**: Responsive layout for all devices

### Symbol System
- **Standard Symbols**: 9, 10, J, Q, K, A (card theme)
- **Special Symbols**:
  - 🌟 **Scatter (Golden Star)**: Triggers free spins
  - 🃏 **Little Joker**: Single wild (substitutes all except scatter)
  - 🎭 **Big Joker**: Expanding wild (covers entire reel)
  - 💳 **Golden Card**: Can transform into wilds

### Bonus Features

#### Free Spins
- **Trigger**: 3+ Scatters = 10 free spins
- **Retrigger**: 3+ Scatters during free spins = +5 spins
- **Enhanced Multipliers**: Start at x2, increase by x2 per cascade (max x10)

#### Wild Transformations
- **Golden Card → Little Joker**: 5% chance on non-winning spins
- **Golden Card → Big Joker**: 2% chance on non-winning spins

#### Multiplier System
- **Base Game**: +1x per cascade (max x5)
- **Free Spins**: +2x per cascade (max x10)

## 🚀 Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Local web server (for development)

### Installation
1. Clone or download the project files
2. Ensure all files are in the correct structure:
```
vegas-ace-slots/
├── index.html
├── css/
│   └── styles.css
├── js/
│   ├── main.js
│   ├── GameEngine.js
│   ├── SymbolsManager.js
│   ├── BonusSystem.js
│   ├── WinCalculator.js
│   └── UIManager.js
└── README.md
```

### Running the Game
1. **Local Server** (Recommended):
   ```bash
   # Using Python 3
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```
   Then open `http://localhost:8000` in your browser

2. **Direct File**: Open `index.html` directly in your browser (may have limitations)

## 🎮 How to Play

### Basic Controls
- **SPIN**: Start a new spin (Space bar)
- **BET +/-**: Adjust bet amount (Arrow keys)
- **MAX BET**: Set maximum affordable bet (M key)

### Betting
- **Minimum Bet**: $0.10
- **Maximum Bet**: $100.00
- **Starting Balance**: $1,000.00

### Winning
- **Ways to Win**: Match 3+ symbols left-to-right on adjacent reels
- **Cascading**: Winning symbols disappear, new ones fall down
- **Multipliers**: Increase with each cascade in the same spin

## 🔧 Technical Details

### Architecture
- **Framework**: Phaser.js 3.60
- **Language**: ES6 JavaScript with modules
- **Styling**: CSS3 with animations
- **Performance**: Optimized for 60fps

### Code Structure
- **GameEngine.js**: Core game logic and state management
- **SymbolsManager.js**: Symbol generation, weights, and transformations
- **BonusSystem.js**: Free spins and wild transformation logic
- **WinCalculator.js**: Ways-to-win calculation and cascading
- **UIManager.js**: User interface and animations

### Math Model
- **RTP**: 97% (Return to Player)
- **Volatility**: Low-medium (frequent small wins)
- **Max Win**: 1,500x bet
- **Hit Frequency**: ~25% (1 in 4 spins wins)

## 🎨 Customization

### Symbol Weights
Modify symbol probabilities in `SymbolsManager.js`:
```javascript
this.symbolWeights = [
    { symbol: '9', weight: 35, payout: [0, 0, 5, 25, 100] },
    { symbol: '10', weight: 30, payout: [0, 0, 5, 30, 125] },
    // ... more symbols
];
```

### Visual Themes
Update colors and styling in `css/styles.css`:
```css
:root {
    --primary-color: #ffd700;
    --secondary-color: #ff6b35;
    --background-gradient: linear-gradient(135deg, #1a1a2e 0%, #0f3460 100%);
}
```

### Game Configuration
Adjust game settings in `main.js`:
```javascript
this.config = {
    reels: 5,
    rows: 4,
    waysToWin: 1024,
    rtp: 97,
    maxWin: 1500,
    // ... more settings
};
```

## 📱 Mobile Support

The game is fully responsive and optimized for mobile devices:
- Touch controls for all interactions
- Adaptive layout for different screen sizes
- Optimized performance for mobile browsers
- Portrait and landscape orientation support

## 🐛 Troubleshooting

### Common Issues
1. **Game won't load**: Ensure you're using a web server, not opening the file directly
2. **Symbols not showing**: Check browser console for texture loading errors
3. **Performance issues**: Try reducing animation quality in browser settings
4. **Mobile controls not working**: Ensure touch events are enabled

### Browser Compatibility
- **Chrome**: 80+ ✅
- **Firefox**: 75+ ✅
- **Safari**: 13+ ✅
- **Edge**: 80+ ✅
- **Mobile Safari**: 13+ ✅
- **Chrome Mobile**: 80+ ✅

## 🔮 Future Enhancements

### Planned Features
- [ ] Sound effects and background music
- [ ] Additional bonus rounds
- [ ] Progressive jackpot system
- [ ] Tournament mode
- [ ] Achievement system
- [ ] Save/load game state
- [ ] Statistics tracking
- [ ] Multiple themes

### Performance Optimizations
- [ ] Object pooling for symbols
- [ ] Texture atlasing
- [ ] WebGL renderer optimization
- [ ] Memory usage optimization

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.

## 📞 Support

For support or questions, please open an issue in the project repository.

---

**Enjoy playing Vegas Ace Slots! 🎰✨**
