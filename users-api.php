<?php
// 🔐 VEGAS ACE SLOTS - USER AUTHENTICATION API 🔐
// Backend API for user registration and login with file storage

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Users data file
$usersFile = 'users_data.json';

// Initialize users file if it doesn't exist
if (!file_exists($usersFile)) {
    file_put_contents($usersFile, json_encode([]));
}

// Get request data
$input = json_decode(file_get_contents('php://input'), true);
$action = $_GET['action'] ?? '';

// Route requests
switch ($action) {
    case 'register':
        handleRegister($input, $usersFile);
        break;
    case 'login':
        handleLogin($input, $usersFile);
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

function handleRegister($data, $usersFile) {
    // Validation
    if (empty($data['name']) || empty($data['mobile']) || empty($data['password'])) {
        echo json_encode(['success' => false, 'message' => 'Please fill in all required fields!']);
        return;
    }

    if (strlen($data['password']) < 6) {
        echo json_encode(['success' => false, 'message' => 'Password must be at least 6 characters!']);
        return;
    }

    // Load existing users
    $users = json_decode(file_get_contents($usersFile), true);

    // Check if user already exists
    foreach ($users as $user) {
        if ($user['mobile'] === $data['mobile']) {
            echo json_encode(['success' => false, 'message' => 'User with this mobile number already exists!']);
            return;
        }
    }

    // Create new user
    $newUser = [
        'id' => 'VA' . time() . rand(100, 999),
        'name' => $data['name'],
        'mobile' => $data['mobile'],
        'email' => $data['email'] ?? '',
        'password' => password_hash($data['password'], PASSWORD_DEFAULT),
        'balance' => 1000.00,
        'joinDate' => date('Y-m-d H:i:s'),
        'lastLogin' => date('Y-m-d H:i:s'),
        'totalDeposits' => 0,
        'totalWithdrawals' => 0,
        'totalSpins' => 0,
        'totalWins' => 0,
        'isActive' => true
    ];

    // Add user to array
    $users[] = $newUser;

    // Save to file
    if (file_put_contents($usersFile, json_encode($users, JSON_PRETTY_PRINT))) {
        // Return user data without password
        $userResponse = $newUser;
        unset($userResponse['password']);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Account created successfully!',
            'user' => $userResponse
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to save user data']);
    }
}

function handleLogin($data, $usersFile) {
    // Validation
    if (empty($data['mobile']) || empty($data['password'])) {
        echo json_encode(['success' => false, 'message' => 'Please enter mobile number and password!']);
        return;
    }

    // Load users
    $users = json_decode(file_get_contents($usersFile), true);

    // Find user
    foreach ($users as $index => $user) {
        if ($user['mobile'] === $data['mobile']) {
            // Verify password
            if (password_verify($data['password'], $user['password'])) {
                // Update last login
                $users[$index]['lastLogin'] = date('Y-m-d H:i:s');
                file_put_contents($usersFile, json_encode($users, JSON_PRETTY_PRINT));

                // Return user data without password
                $userResponse = $user;
                unset($userResponse['password']);
                
                echo json_encode([
                    'success' => true, 
                    'message' => 'Login successful!',
                    'user' => $userResponse
                ]);
                return;
            }
        }
    }

    echo json_encode(['success' => false, 'message' => 'Invalid mobile number or password!']);
}
?>
