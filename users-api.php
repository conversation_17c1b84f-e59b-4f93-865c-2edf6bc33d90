<?php
// 🔐 VEGAS ACE SLOTS - USER AUTHENTICATION API 🔐
// Backend API for user registration and login with file storage

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Users data file
$usersFile = 'users_data.json';

// Initialize users file if it doesn't exist
if (!file_exists($usersFile)) {
    file_put_contents($usersFile, json_encode([]));
}

// Get request data
$rawInput = file_get_contents('php://input');
error_log('Raw input received: ' . $rawInput);
$input = json_decode($rawInput, true);
error_log('Parsed input: ' . print_r($input, true));
$action = $_GET['action'] ?? '';
error_log('Action: ' . $action);

// Route requests
switch ($action) {
    case 'register':
        handleRegister($input, $usersFile);
        break;
    case 'login':
        handleLogin($input, $usersFile);
        break;
    case 'deposit':
        handleDeposit($input, $usersFile);
        break;
    case 'withdraw':
        handleWithdraw($input, $usersFile);
        break;
    case 'update_balance':
        handleUpdateBalance($input, $usersFile);
        break;
    case 'get_analytics':
        handleGetAnalytics($input, $usersFile);
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

function handleRegister($data, $usersFile) {
    // Debug: Log received data
    error_log('Register data received: ' . print_r($data, true));

    // Validation
    if (empty($data['name']) || empty($data['mobile']) || empty($data['password'])) {
        error_log('Validation failed - missing fields');
        echo json_encode(['success' => false, 'message' => 'Please fill in all required fields!']);
        return;
    }

    if (strlen($data['password']) < 6) {
        echo json_encode(['success' => false, 'message' => 'Password must be at least 6 characters!']);
        return;
    }

    // Load existing users
    $users = json_decode(file_get_contents($usersFile), true);

    // Check if user already exists
    foreach ($users as $user) {
        if ($user['mobile'] === $data['mobile']) {
            echo json_encode(['success' => false, 'message' => 'User with this mobile number already exists!']);
            return;
        }
    }

    // Create new user
    $newUser = [
        'id' => 'VA' . time() . rand(100, 999),
        'name' => $data['name'],
        'mobile' => $data['mobile'],
        'email' => $data['email'] ?? '',
        'password' => password_hash($data['password'], PASSWORD_DEFAULT),
        'balance' => 1000.00,
        'joinDate' => date('Y-m-d H:i:s'),
        'lastLogin' => date('Y-m-d H:i:s'),
        'totalDeposits' => 0,
        'totalWithdrawals' => 0,
        'totalSpins' => 0,
        'totalWins' => 0,
        'isActive' => true
    ];

    // Add user to array
    $users[] = $newUser;

    // Save to file
    if (file_put_contents($usersFile, json_encode($users, JSON_PRETTY_PRINT))) {
        // Return user data without password
        $userResponse = $newUser;
        unset($userResponse['password']);

        echo json_encode([
            'success' => true,
            'message' => 'Account created successfully!',
            'user' => $userResponse
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to save user data']);
    }
}

function handleLogin($data, $usersFile) {
    // Validation
    if (empty($data['mobile']) || empty($data['password'])) {
        echo json_encode(['success' => false, 'message' => 'Please enter mobile number and password!']);
        return;
    }

    // Load users
    $users = json_decode(file_get_contents($usersFile), true);

    // Find user
    foreach ($users as $index => $user) {
        if ($user['mobile'] === $data['mobile']) {
            // Verify password
            if (password_verify($data['password'], $user['password'])) {
                // Update last login
                $users[$index]['lastLogin'] = date('Y-m-d H:i:s');
                file_put_contents($usersFile, json_encode($users, JSON_PRETTY_PRINT));

                // Return user data without password
                $userResponse = $user;
                unset($userResponse['password']);

                echo json_encode([
                    'success' => true,
                    'message' => 'Login successful!',
                    'user' => $userResponse
                ]);
                return;
            }
        }
    }

    echo json_encode(['success' => false, 'message' => 'Invalid mobile number or password!']);
}

function handleDeposit($data, $usersFile) {
    // Validation
    if (empty($data['user_id']) || empty($data['amount'])) {
        echo json_encode(['success' => false, 'message' => 'User ID and amount required!']);
        return;
    }

    $amount = floatval($data['amount']);
    if ($amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid deposit amount!']);
        return;
    }

    // Load users
    $users = json_decode(file_get_contents($usersFile), true);

    // Find and update user
    foreach ($users as $index => $user) {
        if ($user['id'] === $data['user_id']) {
            $users[$index]['balance'] += $amount;
            $users[$index]['totalDeposits'] += $amount;
            $users[$index]['lastLogin'] = date('Y-m-d H:i:s');

            // Add transaction record
            if (!isset($users[$index]['transactions'])) {
                $users[$index]['transactions'] = [];
            }
            $users[$index]['transactions'][] = [
                'type' => 'deposit',
                'amount' => $amount,
                'date' => date('Y-m-d H:i:s'),
                'method' => $data['method'] ?? 'card'
            ];

            // Save to file
            if (file_put_contents($usersFile, json_encode($users, JSON_PRETTY_PRINT))) {
                $userResponse = $users[$index];
                unset($userResponse['password']);

                echo json_encode([
                    'success' => true,
                    'message' => 'Deposit successful!',
                    'user' => $userResponse
                ]);
                return;
            }
        }
    }

    echo json_encode(['success' => false, 'message' => 'User not found!']);
}

function handleWithdraw($data, $usersFile) {
    // Validation
    if (empty($data['user_id']) || empty($data['amount'])) {
        echo json_encode(['success' => false, 'message' => 'User ID and amount required!']);
        return;
    }

    $amount = floatval($data['amount']);
    if ($amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid withdrawal amount!']);
        return;
    }

    // Load users
    $users = json_decode(file_get_contents($usersFile), true);

    // Find and update user
    foreach ($users as $index => $user) {
        if ($user['id'] === $data['user_id']) {
            if ($user['balance'] < $amount) {
                echo json_encode(['success' => false, 'message' => 'Insufficient balance!']);
                return;
            }

            $users[$index]['balance'] -= $amount;
            $users[$index]['totalWithdrawals'] += $amount;
            $users[$index]['lastLogin'] = date('Y-m-d H:i:s');

            // Add transaction record
            if (!isset($users[$index]['transactions'])) {
                $users[$index]['transactions'] = [];
            }
            $users[$index]['transactions'][] = [
                'type' => 'withdraw',
                'amount' => $amount,
                'date' => date('Y-m-d H:i:s'),
                'bankAccount' => $data['bankAccount'] ?? '',
                'bankName' => $data['bankName'] ?? ''
            ];

            // Save to file
            if (file_put_contents($usersFile, json_encode($users, JSON_PRETTY_PRINT))) {
                $userResponse = $users[$index];
                unset($userResponse['password']);

                echo json_encode([
                    'success' => true,
                    'message' => 'Withdrawal successful!',
                    'user' => $userResponse
                ]);
                return;
            }
        }
    }

    echo json_encode(['success' => false, 'message' => 'User not found!']);
}

function handleUpdateBalance($data, $usersFile) {
    // For game wins/losses
    if (empty($data['user_id']) || !isset($data['balance'])) {
        echo json_encode(['success' => false, 'message' => 'User ID and balance required!']);
        return;
    }

    // Load users
    $users = json_decode(file_get_contents($usersFile), true);

    // Find and update user
    foreach ($users as $index => $user) {
        if ($user['id'] === $data['user_id']) {
            $oldBalance = $user['balance'];
            $users[$index]['balance'] = floatval($data['balance']);
            $users[$index]['totalSpins'] = ($users[$index]['totalSpins'] ?? 0) + 1;

            if ($data['balance'] > $oldBalance) {
                $users[$index]['totalWins'] = ($users[$index]['totalWins'] ?? 0) + ($data['balance'] - $oldBalance);
            }

            // Save to file
            if (file_put_contents($usersFile, json_encode($users, JSON_PRETTY_PRINT))) {
                echo json_encode(['success' => true, 'message' => 'Balance updated!']);
                return;
            }
        }
    }

    echo json_encode(['success' => false, 'message' => 'User not found!']);
}

function handleGetAnalytics($data, $usersFile) {
    if (empty($data['user_id'])) {
        echo json_encode(['success' => false, 'message' => 'User ID required!']);
        return;
    }

    // Load users
    $users = json_decode(file_get_contents($usersFile), true);

    // Find user
    foreach ($users as $user) {
        if ($user['id'] === $data['user_id']) {
            $analytics = [
                'totalDeposits' => $user['totalDeposits'] ?? 0,
                'totalWithdrawals' => $user['totalWithdrawals'] ?? 0,
                'totalSpins' => $user['totalSpins'] ?? 0,
                'totalWins' => $user['totalWins'] ?? 0,
                'currentBalance' => $user['balance'],
                'transactions' => $user['transactions'] ?? [],
                'winRate' => $user['totalSpins'] > 0 ? round(($user['totalWins'] / $user['totalSpins']) * 100, 2) : 0
            ];

            echo json_encode(['success' => true, 'analytics' => $analytics]);
            return;
        }
    }

    echo json_encode(['success' => false, 'message' => 'User not found!']);
}
?>
