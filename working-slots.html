<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vegas Ace Slots - Working Version</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', monospace;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a0a0a 25%, #2a1a0a 50%, #1a0a1a 75%, #0a0a2a 100%);
            color: #fff;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            animation: subtle-pulse 4s ease-in-out infinite alternate;
        }

        @keyframes subtle-pulse {
            0% { background-size: 100% 100%; }
            100% { background-size: 105% 105%; }
        }

        /* 🔐 NEW DATABASE AUTHENTICATION STYLES */
        .auth-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            font-family: 'Orbitron', monospace;
        }

        .auth-container {
            background: linear-gradient(145deg, #2a2a3e, #1a1a2e);
            border: 3px solid #FFD700;
            border-radius: 20px;
            padding: 40px;
            max-width: 450px;
            width: 90%;
            box-shadow: 0 20px 40px rgba(255, 215, 0, 0.3);
            text-align: center;
        }

        .auth-header h1 {
            color: #FFD700;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        }

        .auth-header p {
            color: #FF6B35;
            font-size: 1.1em;
            margin-bottom: 30px;
        }

        .auth-form h2 {
            color: #FFD700;
            font-size: 1.8em;
            margin-bottom: 25px;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #FFD700;
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.3);
            color: white;
            font-size: 1.1em;
            font-family: 'Orbitron', monospace;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .input-group input:focus {
            outline: none;
            border-color: #FF6B35;
            box-shadow: 0 0 15px rgba(255, 107, 53, 0.5);
            background: rgba(0, 0, 0, 0.5);
        }

        .input-group input::placeholder {
            color: #aaa;
        }

        .auth-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #FFD700, #FF6B35);
            border: none;
            border-radius: 10px;
            color: #1a1a2e;
            font-size: 1.2em;
            font-weight: bold;
            font-family: 'Orbitron', monospace;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .auth-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 215, 0, 0.4);
        }

        .auth-link {
            color: #ccc;
            font-size: 0.9em;
        }

        .auth-link span {
            color: #FFD700;
            cursor: pointer;
            text-decoration: underline;
            transition: color 0.3s ease;
        }

        .auth-link span:hover {
            color: #FF6B35;
        }

        .game-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .header {
            text-align: center;
            padding: 25px;
            background: linear-gradient(145deg, rgba(255,215,0,0.1), rgba(255,69,0,0.1));
            border-radius: 20px;
            margin-bottom: 25px;
            border: 3px solid #ffd700;
            box-shadow: 0 0 30px rgba(255, 215, 0, 0.3), inset 0 0 20px rgba(255, 69, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,215,0,0.1), transparent);
            animation: shine 3s linear infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .title {
            font-size: 3rem;
            color: #ffd700;
            margin-bottom: 20px;
            text-shadow: 0 0 30px rgba(255, 215, 0, 0.8), 0 0 60px rgba(255, 69, 0, 0.4);
            font-weight: 900;
            letter-spacing: 3px;
            position: relative;
            z-index: 1;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 40px;
            flex-wrap: wrap;
        }

        .stat {
            text-align: center;
            background: linear-gradient(145deg, rgba(0,0,0,0.8), rgba(20,20,20,0.9));
            padding: 15px 25px;
            border-radius: 15px;
            border: 2px solid #ffd700;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.2), inset 0 0 10px rgba(255, 215, 0, 0.1);
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
        }

        .stat:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 25px rgba(255, 215, 0, 0.4);
        }

        .stat-label {
            font-size: 1rem;
            color: #ffd700;
            margin-bottom: 8px;
            font-weight: bold;
            letter-spacing: 1px;
        }

        .stat-value {
            font-size: 1.6rem;
            color: #fff;
            font-weight: 900;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .multiplier-stat {
            background: linear-gradient(145deg, rgba(255,69,0,0.8), rgba(255,140,0,0.9));
            border-color: #ff6b35;
            box-shadow: 0 0 25px rgba(255, 69, 0, 0.4), inset 0 0 15px rgba(255, 140, 0, 0.2);
        }

        .multiplier-value {
            color: #fff;
            font-size: 1.8rem;
            text-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
            animation: multiplier-glow 1.5s ease-in-out infinite alternate;
        }

        @keyframes multiplier-glow {
            0% {
                text-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
                transform: scale(1);
            }
            100% {
                text-shadow: 0 0 25px rgba(255, 255, 255, 1), 0 0 35px rgba(255, 69, 0, 0.8);
                transform: scale(1.05);
            }
        }

        .game-area {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
        }

        .slot-machine {
            background: linear-gradient(145deg, rgba(0,0,0,0.9), rgba(30,30,30,0.9));
            border: 4px solid #ffd700;
            border-radius: 25px;
            padding: 40px;
            box-shadow:
                0 0 60px rgba(255, 215, 0, 0.4),
                inset 0 0 30px rgba(255, 69, 0, 0.1),
                0 10px 30px rgba(0, 0, 0, 0.5);
            position: relative;
        }

        .slot-machine::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #ffd700, #ff6b35, #ffd700, #ff6b35);
            border-radius: 25px;
            z-index: -1;
            animation: border-glow 2s linear infinite;
        }

        @keyframes border-glow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .reels {
            display: grid;
            grid-template-columns: repeat(5, 100px);
            grid-template-rows: repeat(4, 100px);
            gap: 10px;
            margin-bottom: 20px;
        }

        .symbol {
            width: 100px;
            height: 100px;
            background: linear-gradient(145deg, #1a1a1a, #0a0a0a);
            border: 3px solid #ffd700;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.2rem;
            font-weight: 900;
            color: #ffd700;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 0 15px rgba(255, 215, 0, 0.3),
                inset 0 0 10px rgba(255, 215, 0, 0.1);
        }

        .symbol::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .symbol:hover::before {
            left: 100%;
        }

        .symbol.spinning {
            animation: spin-blur 0.2s ease-in-out infinite;
            border-color: #ff6b35;
            box-shadow: 0 0 15px rgba(255, 107, 53, 0.6);
        }

        .symbol.winning {
            background: linear-gradient(145deg, #ff6b35, #ff4500);
            box-shadow: 0 0 30px rgba(255, 215, 0, 1);
            animation: win-glow 1s ease-in-out infinite alternate;
            transform: scale(1.1);
        }

        .symbol.dropping {
            animation: drop-in 0.8s ease-out;
        }

        .controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            padding: 20px;
            background: rgba(0,0,0,0.4);
            border-radius: 15px;
            border: 2px solid #ffd700;
            flex-wrap: wrap;
        }

        .bet-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .btn {
            padding: 12px 20px;
            background: linear-gradient(145deg, #2a2a3e, #1a1a2e);
            border: 2px solid #ffd700;
            color: #ffd700;
            font-family: 'Orbitron', monospace;
            font-weight: bold;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .btn:hover:not(:disabled) {
            background: linear-gradient(145deg, #3a3a4e, #2a2a3e);
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.4);
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .spin-btn {
            padding: 20px 40px;
            background: linear-gradient(145deg, #ff6b35, #ff4500, #ff6b35);
            background-size: 200% 200%;
            color: white;
            font-size: 1.5rem;
            font-weight: 900;
            border: 4px solid #ffd700;
            min-width: 180px;
            border-radius: 15px;
            text-shadow: 0 0 10px rgba(0,0,0,0.8);
            box-shadow:
                0 0 30px rgba(255, 107, 53, 0.6),
                inset 0 0 20px rgba(255, 255, 255, 0.1);
            animation: pulse-glow 2s ease-in-out infinite alternate;
            position: relative;
            overflow: hidden;
        }

        .spin-btn::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: spin-shine 3s linear infinite;
        }

        @keyframes pulse-glow {
            0% {
                box-shadow: 0 0 30px rgba(255, 107, 53, 0.6), inset 0 0 20px rgba(255, 255, 255, 0.1);
                background-position: 0% 50%;
            }
            100% {
                box-shadow: 0 0 50px rgba(255, 107, 53, 1), inset 0 0 30px rgba(255, 255, 255, 0.2);
                background-position: 100% 50%;
            }
        }

        @keyframes spin-shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .spin-btn:hover:not(:disabled) {
            background: linear-gradient(145deg, #ff7b45, #ff5500, #ff7b45);
            transform: translateY(-3px);
            box-shadow: 0 5px 35px rgba(255, 107, 53, 0.8);
        }

        .bet-display {
            background: rgba(0,0,0,0.7);
            padding: 10px 20px;
            border-radius: 10px;
            border: 2px solid #ffd700;
            text-align: center;
        }

        .bet-label {
            font-size: 0.8rem;
            color: #ccc;
            margin-bottom: 5px;
        }

        .bet-value {
            font-size: 1.2rem;
            color: #ffd700;
            font-weight: bold;
        }

        .bonus-displays {
            position: fixed;
            top: 50%;
            transform: translateY(-50%);
            z-index: 100;
        }

        .multiplier-display {
            left: 20px;
            background: rgba(0,0,0,0.9);
            border: 2px solid #ffd700;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .freespins-display {
            right: 20px;
            background: rgba(138, 43, 226, 0.9);
            border: 2px solid #dda0dd;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .bonus-text {
            font-size: 0.9rem;
            color: #ccc;
            margin-bottom: 5px;
        }

        .bonus-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #ffd700;
        }

        .freespins-display .bonus-value {
            color: #dda0dd;
        }

        .win-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.95);
            border: 3px solid #ffd700;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            z-index: 1000;
            box-shadow: 0 0 50px rgba(255, 215, 0, 0.8);
        }

        .win-amount {
            font-size: 3rem;
            color: #ffd700;
            font-weight: 900;
            margin-bottom: 15px;
            animation: win-glow 1s ease-in-out infinite alternate;
        }

        .win-type {
            font-size: 1.5rem;
            color: #ff6b35;
            font-weight: bold;
        }

        .message {
            position: fixed;
            top: 20%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.9);
            color: #ffd700;
            padding: 20px 40px;
            border-radius: 10px;
            border: 2px solid #ffd700;
            font-size: 1.2rem;
            font-weight: bold;
            z-index: 1500;
            text-align: center;
        }

        .hidden {
            display: none !important;
        }

        @keyframes spin-blur {
            0% {
                filter: blur(0px);
                transform: scale(1) rotateY(0deg);
                opacity: 1;
            }
            25% {
                filter: blur(2px);
                transform: scale(0.9) rotateY(90deg);
                opacity: 0.8;
            }
            50% {
                filter: blur(4px);
                transform: scale(0.85) rotateY(180deg);
                opacity: 0.6;
            }
            75% {
                filter: blur(2px);
                transform: scale(0.9) rotateY(270deg);
                opacity: 0.8;
            }
            100% {
                filter: blur(0px);
                transform: scale(1) rotateY(360deg);
                opacity: 1;
            }
        }

        @keyframes win-glow {
            0% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
            100% { box-shadow: 0 0 40px rgba(255, 215, 0, 1), 0 0 60px rgba(255, 107, 53, 0.8); }
        }

        @keyframes drop-in {
            0% { transform: translateY(-150px) rotate(180deg); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateY(0) rotate(0deg); opacity: 1; }
        }

        @keyframes flash {
            0%, 100% { background-color: transparent; }
            50% { background-color: rgba(255, 215, 0, 0.3); }
        }

        @media (max-width: 768px) {
            .reels {
                grid-template-columns: repeat(5, 70px);
                grid-template-rows: repeat(4, 70px);
                gap: 5px;
            }
            .symbol {
                width: 70px;
                height: 70px;
                font-size: 1.5rem;
            }
            .controls {
                flex-direction: column;
                gap: 15px;
            }
            .stats {
                gap: 20px;
            }
        }
        /* Authentication System Styles */
        .modal {
            display: flex;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: linear-gradient(145deg, #1a1a2e, #16213e);
            border: 3px solid #ffd700;
            border-radius: 20px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 0 50px rgba(255, 215, 0, 0.5);
            position: relative;
        }

        .auth-container h2, .profile-container h2 {
            color: #ffd700;
            text-align: center;
            margin-bottom: 25px;
            font-size: 1.8rem;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .auth-form h3, .profile-container h3 {
            color: #fff;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            color: #ffd700;
            margin-bottom: 8px;
            font-weight: bold;
        }

        .input-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ffd700;
            border-radius: 8px;
            background: rgba(0,0,0,0.7);
            color: #fff;
            font-family: 'Orbitron', monospace;
            font-size: 1rem;
        }

        .input-group input:focus {
            outline: none;
            border-color: #ff6b35;
            box-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
        }

        .auth-btn, .action-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(145deg, #ff6b35, #ff4500);
            border: 2px solid #ffd700;
            color: white;
            font-family: 'Orbitron', monospace;
            font-weight: bold;
            font-size: 1.1rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .auth-btn:hover, .action-btn:hover {
            background: linear-gradient(145deg, #ff7b45, #ff5500);
            box-shadow: 0 0 20px rgba(255, 107, 53, 0.6);
            transform: translateY(-2px);
        }

        .auth-switch {
            text-align: center;
            color: #ccc;
        }

        .auth-switch span {
            color: #ffd700;
            cursor: pointer;
            text-decoration: underline;
        }

        .auth-switch span:hover {
            color: #ff6b35;
        }

        .profile-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .close-btn {
            background: #ff4444;
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }

        .close-btn:hover {
            background: #ff6666;
        }

        .user-details, .balance-section, .activity-section, .transaction-history {
            background: rgba(0,0,0,0.3);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #ffd700;
        }

        .balance-display-large {
            font-size: 2.5rem;
            color: #ffd700;
            text-align: center;
            margin: 15px 0;
            font-weight: bold;
            text-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
        }

        .balance-actions {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .action-btn.deposit {
            background: linear-gradient(145deg, #28a745, #20c997);
        }

        .action-btn.withdraw {
            background: linear-gradient(145deg, #dc3545, #fd7e14);
        }

        .action-btn.cancel {
            background: linear-gradient(145deg, #6c757d, #495057);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background: rgba(255, 215, 0, 0.1);
            border-radius: 5px;
        }

        .stat-label {
            color: #ccc;
        }

        .stat-value {
            color: #ffd700;
            font-weight: bold;
        }

        .transaction-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .transaction-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            border-bottom: 1px solid #333;
            margin-bottom: 5px;
        }

        .transaction-item:last-child {
            border-bottom: none;
        }

        .transaction-type {
            font-weight: bold;
        }

        .transaction-type.deposit {
            color: #28a745;
        }

        .transaction-type.withdraw {
            color: #dc3545;
        }

        .transaction-type.bet {
            color: #ffc107;
        }

        .transaction-type.win {
            color: #17a2b8;
        }

        .no-transactions {
            text-align: center;
            color: #666;
            font-style: italic;
        }

        .payment-methods {
            margin: 20px 0;
        }

        .payment-options {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .payment-option {
            display: flex;
            align-items: center;
            padding: 10px;
            background: rgba(0,0,0,0.3);
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .payment-option:hover {
            background: rgba(255, 215, 0, 0.1);
        }

        .payment-option input {
            margin-right: 10px;
        }

        .current-balance {
            background: rgba(255, 215, 0, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.2rem;
            color: #ffd700;
        }

        .withdrawal-info {
            margin: 20px 0;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-controls {
            display: flex;
            gap: 10px;
        }

        .user-btn {
            padding: 8px 15px;
            background: linear-gradient(145deg, #2a2a3e, #1a1a2e);
            border: 2px solid #ffd700;
            color: #ffd700;
            font-family: 'Orbitron', monospace;
            font-weight: bold;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .user-btn:hover {
            background: linear-gradient(145deg, #3a3a4e, #2a2a3e);
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.4);
            transform: translateY(-1px);
        }

        @media (max-width: 768px) {
            .modal-content {
                padding: 20px;
                margin: 10px;
            }

            .balance-actions {
                flex-direction: column;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .user-info {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>


    <!-- User Profile Modal -->
    <div id="profile-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="profile-container">
                <div class="profile-header">
                    <h2>👤 User Profile</h2>
                    <button id="close-profile" class="close-btn">✖</button>
                </div>

                <div class="profile-info">
                    <div class="user-details">
                        <h3>Account Information</h3>
                        <p><strong>Name:</strong> <span id="profile-name">-</span></p>
                        <p><strong>Mobile:</strong> <span id="profile-mobile">-</span></p>
                        <p><strong>Account ID:</strong> <span id="profile-id">-</span></p>
                        <p><strong>Member Since:</strong> <span id="profile-joined">-</span></p>
                    </div>

                    <div class="balance-section">
                        <h3>💰 Account Balance</h3>
                        <div class="balance-display-large">
                            $<span id="profile-balance">0.00</span>
                        </div>

                        <div class="balance-actions">
                            <button id="deposit-btn" class="action-btn deposit">💳 DEPOSIT</button>
                            <button id="withdraw-btn" class="action-btn withdraw">💸 WITHDRAW</button>
                        </div>
                    </div>
                </div>

                <div class="activity-section">
                    <h3>📊 Account Activity</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">Total Deposits:</span>
                            <span class="stat-value">$<span id="total-deposits">0.00</span></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Total Withdrawals:</span>
                            <span class="stat-value">$<span id="total-withdrawals">0.00</span></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Total Wagered:</span>
                            <span class="stat-value">$<span id="total-wagered">0.00</span></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Total Won:</span>
                            <span class="stat-value">$<span id="total-won">0.00</span></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Games Played:</span>
                            <span class="stat-value"><span id="games-played">0</span></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Win Rate:</span>
                            <span class="stat-value"><span id="win-rate">0%</span></span>
                        </div>
                    </div>
                </div>

                <div class="transaction-history">
                    <h3>📋 Recent Transactions</h3>
                    <div id="transaction-list" class="transaction-list">
                        <p class="no-transactions">No transactions yet</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Deposit Modal -->
    <div id="deposit-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="transaction-container">
                <h2>💳 Deposit Funds</h2>
                <div class="input-group">
                    <label>💰 Amount to Deposit:</label>
                    <input type="number" id="deposit-amount" placeholder="Enter amount" min="10" max="10000" step="0.01">
                </div>
                <div class="payment-methods">
                    <h3>Payment Method:</h3>
                    <div class="payment-options">
                        <label class="payment-option">
                            <input type="radio" name="deposit-method" value="card" checked>
                            <span>💳 Credit/Debit Card</span>
                        </label>
                        <label class="payment-option">
                            <input type="radio" name="deposit-method" value="bank">
                            <span>🏦 Bank Transfer</span>
                        </label>
                        <label class="payment-option">
                            <input type="radio" name="deposit-method" value="wallet">
                            <span>📱 Digital Wallet</span>
                        </label>
                    </div>
                </div>
                <div class="transaction-actions">
                    <button id="confirm-deposit" class="action-btn deposit">✅ CONFIRM DEPOSIT</button>
                    <button id="cancel-deposit" class="action-btn cancel">❌ CANCEL</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Withdraw Modal -->
    <div id="withdraw-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="transaction-container">
                <h2>💸 Withdraw Funds</h2>
                <div class="current-balance">
                    <p>Available Balance: $<span id="withdraw-available">0.00</span></p>
                </div>
                <div class="input-group">
                    <label>💰 Amount to Withdraw:</label>
                    <input type="number" id="withdraw-amount" placeholder="Enter amount" min="10" step="0.01">
                </div>
                <div class="withdrawal-info">
                    <h3>Withdrawal Information:</h3>
                    <div class="input-group">
                        <label>🏦 Bank Account Number:</label>
                        <input type="text" id="bank-account" placeholder="Enter bank account number">
                    </div>
                    <div class="input-group">
                        <label>🏛️ Bank Name:</label>
                        <input type="text" id="bank-name" placeholder="Enter bank name">
                    </div>
                </div>
                <div class="transaction-actions">
                    <button id="confirm-withdraw" class="action-btn withdraw">✅ CONFIRM WITHDRAWAL</button>
                    <button id="cancel-withdraw" class="action-btn cancel">❌ CANCEL</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 🔐 CLEAN DATABASE AUTHENTICATION SYSTEM -->
    <div id="auth-modal" class="auth-modal">
        <div class="auth-container">
            <div class="auth-header">
                <h1>🎰 VEGAS ACE SLOTS 🎰</h1>
                <p>Professional Casino Experience</p>
            </div>

            <!-- Login Form -->
            <div id="login-form" class="auth-form">
                <h2>🔑 LOGIN</h2>
                <div class="input-group">
                    <input type="tel" id="login-mobile" placeholder="Mobile Number" required>
                </div>
                <div class="input-group">
                    <input type="password" id="login-password" placeholder="Password" required>
                </div>
                <button id="login-btn" class="auth-btn">LOGIN</button>
                <p class="auth-link">Don't have an account? <span id="show-register" style="cursor: pointer; color: #FFD700; text-decoration: underline;">Register here</span></p>
            </div>

            <!-- Register Form -->
            <div id="register-form" class="auth-form" style="display: none;">
                <h2>📝 REGISTER</h2>
                <div class="input-group">
                    <input type="text" id="register-name" placeholder="Full Name" required>
                </div>
                <div class="input-group">
                    <input type="tel" id="register-mobile" placeholder="Mobile Number" required>
                </div>
                <div class="input-group">
                    <input type="email" id="register-email" placeholder="Email (Optional)">
                </div>
                <div class="input-group">
                    <input type="password" id="register-password" placeholder="Password (min 6 chars)" required>
                </div>
                <div class="input-group">
                    <input type="password" id="register-confirm" placeholder="Confirm Password" required>
                </div>
                <button id="register-btn" class="auth-btn">CREATE ACCOUNT</button>
                <p class="auth-link">Already have an account? <span id="show-login" style="cursor: pointer; color: #FFD700; text-decoration: underline;">Login here</span></p>
            </div>
        </div>
    </div>

    <div class="game-container" id="game-container" style="display: none;">
        <div class="header">
            <h1 class="title">VEGAS ACE SLOTS</h1>
            <div class="user-info">
                <div class="stats">
                    <div class="stat">
                        <div class="stat-label">BALANCE</div>
                        <div class="stat-value" id="balance">$0.00</div>
                    </div>
                    <div class="stat">
                        <div class="stat-label">BET</div>
                        <div class="stat-value" id="current-bet">$1.00</div>
                    </div>
                    <div class="stat">
                        <div class="stat-label">LAST WIN</div>
                        <div class="stat-value" id="last-win">$0.00</div>
                    </div>
                    <div class="stat multiplier-stat" id="header-multiplier">
                        <div class="stat-label">MULTIPLIER</div>
                        <div class="stat-value multiplier-value" id="header-multiplier-value">1X</div>
                    </div>
                </div>
                <div class="user-controls">
                    <button id="profile-btn" class="user-btn">👤 PROFILE</button>
                    <button id="logout-btn" class="user-btn">🚪 LOGOUT</button>
                </div>
            </div>
        </div>

        <div class="game-area">
            <div class="slot-machine">
                <div class="reels" id="reels">
                    <!-- Symbols will be generated here -->
                </div>
            </div>
        </div>

        <div class="controls">
            <div class="bet-section">
                <button class="btn" id="bet-down">BET -</button>
                <div class="bet-display">
                    <div class="bet-label">CURRENT BET</div>
                    <div class="bet-value" id="bet-display">$1.00</div>
                </div>
                <button class="btn" id="bet-up">BET +</button>
            </div>

            <button class="btn spin-btn" id="spin-button">
                <span id="spin-text">SPIN</span>
            </button>

            <button class="btn" id="max-bet">MAX BET</button>

            <button class="btn" id="music-toggle">🎼 MUSIC ON</button>
        </div>

        <!-- 📊 SMART ANALYTICS DISPLAY -->
        <div id="analytics-display" style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.9); color: #FFD700; padding: 12px; border-radius: 8px; font-size: 11px; max-width: 220px; border: 2px solid #FFD700; font-family: 'Orbitron', monospace; z-index: 100;">
            <div style="text-align: center; font-weight: bold; margin-bottom: 8px; color: #FF6B35;">🎯 SMART SYSTEM</div>
            <div>Spins: <span id="analytics-spins" style="color: #00FF00;">0</span></div>
            <div>RTP: <span id="analytics-rtp" style="color: #00FF00;">0%</span></div>
            <div>Type: <span id="analytics-type" style="color: #FFD700;">newPlayer</span></div>
            <div>Reels: <span id="analytics-reels" style="color: #FF6B35;">standard</span></div>
            <div>Loss Streak: <span id="analytics-losses" style="color: #FF4444;">0</span></div>
            <div>No Win: <span id="analytics-nowins" style="color: #FF4444;">0</span></div>
            <div>No Bonus: <span id="analytics-nobonus" style="color: #FFAA00;">0</span></div>
            <div style="margin-top: 5px; font-size: 10px; color: #888;">Cooldown: <span id="analytics-cooldown" style="color: #FFAA00;">0</span></div>
        </div>

        <!-- Bonus Displays -->
        <div class="bonus-displays">
            <div class="multiplier-display hidden" id="multiplier-display">
                <div class="bonus-text">MULTIPLIER</div>
                <div class="bonus-value" id="multiplier-value">x1</div>
            </div>
            <div class="freespins-display hidden" id="freespins-display">
                <div class="bonus-text">FREE SPINS</div>
                <div class="bonus-value" id="freespins-value">0</div>
            </div>
        </div>

        <!-- Win Popup -->
        <div class="win-popup hidden" id="win-popup">
            <div class="win-amount" id="win-amount-display">$0.00</div>
            <div class="win-type" id="win-type-display">WIN!</div>
        </div>

        <!-- Message Display -->
        <div class="message hidden" id="message-display"></div>
    </div>

    <!-- Simple HTML5 Audio System -->
    <audio id="bgMusic" loop>
        <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
    </audio>
    <audio id="spinSound">
        <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
    </audio>
    <audio id="winSound">
        <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
    </audio>
    <script>
        // 🔐 VEGAS ACE SLOTS - CLEAN DATABASE AUTHENTICATION SYSTEM 🔐
        // Professional casino with complete user management and analytics

        // Global user data
        let currentUser = null;

        // 🎯 CLEAN AUTHENTICATION SYSTEM
        class VegasAceAuth {
            constructor() {
                this.init();
            }

            init() {
                console.log('🚀 Initializing Vegas Ace Authentication System');
                this.setupEventListeners();
                this.showAuthModal();
            }

            setupEventListeners() {
                // Login button
                document.getElementById('login-btn').addEventListener('click', () => {
                    this.handleLogin();
                });

                // Register button
                document.getElementById('register-btn').addEventListener('click', () => {
                    console.log('🔥 REGISTER BUTTON CLICKED!');
                    this.handleRegister();
                });

                // Form switching
                document.getElementById('show-register').addEventListener('click', () => {
                    this.showRegisterForm();
                });

                document.getElementById('show-login').addEventListener('click', () => {
                    this.showLoginForm();
                });

                // Enter key support
                document.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        const loginForm = document.getElementById('login-form');
                        const registerForm = document.getElementById('register-form');

                        if (loginForm.style.display !== 'none') {
                            this.handleLogin();
                        } else if (registerForm.style.display !== 'none') {
                            this.handleRegister();
                        }
                    }
                });
            }

            async handleLogin() {
                const mobile = document.getElementById('login-mobile').value.trim();
                const password = document.getElementById('login-password').value;

                // Validation
                if (!mobile || !password) {
                    this.showMessage('❌ Please enter mobile number and password!', 'error');
                    return;
                }

                try {
                    const response = await fetch('users-api.php?action=login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            mobile: mobile,
                            password: password
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        this.showMessage('🎯 Login successful! Welcome back ' + data.user.name + '!', 'success');
                        setTimeout(() => {
                            this.loginUser(data.user);
                        }, 1500);
                    } else {
                        this.showMessage('❌ ' + data.message, 'error');
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    this.showMessage('❌ Connection error. Please try again.', 'error');
                }
            }

            async handleRegister() {
                console.log('🚀 HANDLE REGISTER FUNCTION CALLED!');

                const name = document.getElementById('register-name').value.trim();
                const mobile = document.getElementById('register-mobile').value.trim();
                const email = document.getElementById('register-email').value.trim() || '';
                const password = document.getElementById('register-password').value;
                const confirmPassword = document.getElementById('register-confirm').value;

                console.log('📝 Form data:', { name, mobile, email, password: '***', confirmPassword: '***' });

                // Validation
                if (!name || !mobile || !password || !confirmPassword) {
                    console.log('❌ Validation failed: Missing required fields');
                    this.showMessage('❌ Please fill in all required fields!', 'error');
                    return;
                }

                if (password.length < 6) {
                    this.showMessage('❌ Password must be at least 6 characters!', 'error');
                    return;
                }

                if (password !== confirmPassword) {
                    this.showMessage('❌ Passwords do not match!', 'error');
                    return;
                }

                try {
                    console.log('📡 Sending registration request to API...');
                    const response = await fetch('users-api.php?action=register', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            name: name,
                            mobile: mobile,
                            email: email,
                            password: password
                        })
                    });

                    console.log('📡 API Response status:', response.status);
                    const data = await response.json();
                    console.log('📡 API Response data:', data);

                    if (data.success) {
                        this.showMessage('🎊 Account created successfully! Welcome to Vegas Ace Slots!', 'success');
                        setTimeout(() => {
                            this.loginUser(data.user);
                        }, 2000);
                    } else {
                        this.showMessage('❌ ' + data.message, 'error');
                    }
                } catch (error) {
                    console.error('Registration error:', error);
                    this.showMessage('❌ Connection error. Please try again.', 'error');
                }
            }

            showRegisterForm() {
                document.getElementById('login-form').style.display = 'none';
                document.getElementById('register-form').style.display = 'block';
                console.log('📝 Switched to register form');
            }

            showLoginForm() {
                document.getElementById('register-form').style.display = 'none';
                document.getElementById('login-form').style.display = 'block';
                console.log('🔑 Switched to login form');
            }

            loginUser(user) {
                console.log('🔐 Logging in user:', user);
                currentUser = user;
                gameState.balance = user.balance;

                // Store user session in localStorage for persistence
                localStorage.setItem('vegasAceUser', JSON.stringify(user));
                localStorage.setItem('vegasAceLoggedIn', 'true');

                this.hideAuthModal();
                this.showGameInterface();

                // Initialize the game after successful login
                console.log('🎮 Initializing game after login...');
                setTimeout(() => {
                    initGameAfterLogin();
                }, 100);

                // Set up UI event listeners after login
                this.setupUIEventListeners();

                console.log('✅ User logged in:', user.name);
            }

            showAuthModal() {
                document.getElementById('auth-modal').style.display = 'flex';
                document.getElementById('game-container').style.display = 'none';
            }

            hideAuthModal() {
                document.getElementById('auth-modal').style.display = 'none';
            }

            showGameInterface() {
                document.getElementById('game-container').style.display = 'block';
            }

            setupUIEventListeners() {
                console.log('🎮 Setting up UI event listeners...');

                // Profile button
                const profileBtn = document.getElementById('profile-btn');
                if (profileBtn) {
                    profileBtn.addEventListener('click', () => {
                        console.log('👤 Profile button clicked');
                        this.showProfile();
                    });
                }

                // Logout button
                const logoutBtn = document.getElementById('logout-btn');
                if (logoutBtn) {
                    logoutBtn.addEventListener('click', () => {
                        console.log('🚪 Logout button clicked');
                        this.logout();
                    });
                }

                // Profile modal close button
                const closeProfileBtn = document.getElementById('close-profile');
                if (closeProfileBtn) {
                    closeProfileBtn.addEventListener('click', () => {
                        this.hideProfile();
                    });
                }

                // Deposit and withdraw buttons
                const depositBtn = document.getElementById('deposit-btn');
                const withdrawBtn = document.getElementById('withdraw-btn');

                if (depositBtn) {
                    depositBtn.addEventListener('click', () => {
                        this.showDepositModal();
                    });
                }

                if (withdrawBtn) {
                    withdrawBtn.addEventListener('click', () => {
                        this.showWithdrawModal();
                    });
                }

                // Modal close buttons
                const cancelDepositBtn = document.getElementById('cancel-deposit');
                const cancelWithdrawBtn = document.getElementById('cancel-withdraw');

                if (cancelDepositBtn) {
                    cancelDepositBtn.addEventListener('click', () => {
                        document.getElementById('deposit-modal').style.display = 'none';
                    });
                }

                if (cancelWithdrawBtn) {
                    cancelWithdrawBtn.addEventListener('click', () => {
                        document.getElementById('withdraw-modal').style.display = 'none';
                    });
                }

                // Confirm buttons (basic functionality for now)
                const confirmDepositBtn = document.getElementById('confirm-deposit');
                const confirmWithdrawBtn = document.getElementById('confirm-withdraw');

                if (confirmDepositBtn) {
                    confirmDepositBtn.addEventListener('click', () => {
                        this.showMessage('💳 Deposit feature coming soon!', 'info');
                        document.getElementById('deposit-modal').style.display = 'none';
                    });
                }

                if (confirmWithdrawBtn) {
                    confirmWithdrawBtn.addEventListener('click', () => {
                        this.showMessage('💸 Withdrawal feature coming soon!', 'info');
                        document.getElementById('withdraw-modal').style.display = 'none';
                    });
                }

                console.log('✅ UI event listeners set up successfully');
            }

            showProfile() {
                console.log('👤 Showing user profile...');
                if (currentUser) {
                    // Update profile information
                    document.getElementById('profile-name').textContent = currentUser.name;
                    document.getElementById('profile-mobile').textContent = currentUser.mobile;
                    document.getElementById('profile-id').textContent = currentUser.id;
                    document.getElementById('profile-joined').textContent = new Date(currentUser.joinDate).toLocaleDateString();
                    document.getElementById('profile-balance').textContent = currentUser.balance.toFixed(2);

                    // Show profile modal
                    document.getElementById('profile-modal').style.display = 'flex';
                }
            }

            hideProfile() {
                document.getElementById('profile-modal').style.display = 'none';
            }

            logout() {
                console.log('🚪 Logging out user...');
                clearSession();
                this.hideProfile();
                this.showAuthModal();
                this.showMessage('👋 Logged out successfully!', 'info');
            }

            showDepositModal() {
                console.log('💳 Showing deposit modal...');
                document.getElementById('deposit-modal').style.display = 'flex';
            }

            showWithdrawModal() {
                console.log('💸 Showing withdraw modal...');
                if (currentUser) {
                    document.getElementById('withdraw-available').textContent = currentUser.balance.toFixed(2);
                    document.getElementById('withdraw-modal').style.display = 'flex';
                }
            }

            showMessage(text, type = 'info') {
                let messageEl = document.getElementById('auth-message');
                if (!messageEl) {
                    messageEl = document.createElement('div');
                    messageEl.id = 'auth-message';
                    messageEl.style.cssText = `
                        position: fixed;
                        top: 20px;
                        left: 50%;
                        transform: translateX(-50%);
                        padding: 15px 25px;
                        border-radius: 8px;
                        font-weight: bold;
                        z-index: 9999;
                        max-width: 400px;
                        text-align: center;
                        font-family: 'Orbitron', monospace;
                    `;
                    document.body.appendChild(messageEl);
                }

                const styles = {
                    success: 'background: #28a745; color: white; border: 2px solid #1e7e34;',
                    error: 'background: #dc3545; color: white; border: 2px solid #c82333;',
                    info: 'background: #17a2b8; color: white; border: 2px solid #138496;'
                };

                messageEl.style.cssText += styles[type] || styles.info;
                messageEl.textContent = text;
                messageEl.style.display = 'block';

                setTimeout(() => {
                    messageEl.style.display = 'none';
                }, 4000);
            }
        }

        // Initialize authentication system when page loads
        let vegasAuth = null;
        document.addEventListener('DOMContentLoaded', () => {
            vegasAuth = new VegasAceAuth();

            // Check for existing session
            checkExistingSession();
        });

        // 🔐 SESSION PERSISTENCE SYSTEM
        function checkExistingSession() {
            console.log('🔍 Checking for existing session...');

            const isLoggedIn = localStorage.getItem('vegasAceLoggedIn');
            const userData = localStorage.getItem('vegasAceUser');

            if (isLoggedIn === 'true' && userData) {
                try {
                    const user = JSON.parse(userData);
                    console.log('🔄 Restoring session for:', user.name);

                    currentUser = user;
                    gameState.balance = user.balance;

                    // Hide auth modal and show game
                    document.getElementById('auth-modal').style.display = 'none';
                    document.getElementById('game-container').style.display = 'block';

                    // Initialize game
                    setTimeout(() => {
                        initGameAfterLogin();
                    }, 100);

                    // Set up UI event listeners
                    if (vegasAuth) {
                        vegasAuth.setupUIEventListeners();
                    }

                    console.log('✅ Session restored successfully');
                } catch (error) {
                    console.error('❌ Error restoring session:', error);
                    clearSession();
                }
            } else {
                console.log('🔐 No existing session found');
            }
        }

        function clearSession() {
            localStorage.removeItem('vegasAceUser');
            localStorage.removeItem('vegasAceLoggedIn');
            currentUser = null;
            gameState.balance = 1000.00;
        }

        // Game State
        const gameState = {
            balance: 1000.00,
            currentBet: 1.00,
            isSpinning: false,
            inFreeSpins: false,
            freeSpinsRemaining: 0,
            freeSpinsTargetReturn: 0,    // Target amount to return during free spins
            freeSpinsTotalWin: 0,        // Total won during current free spins
            currentMultiplier: 1,
            lastWin: 0,
            consecutiveWins: 0,
            isCoolingSpin: false
        };

        // 🎰 ADVANCED CASINO MATHEMATICS & PSYCHOLOGY ENGINE 🎰
        // Based on professional slot design principles with adaptive RTP and behavioral analysis
        const config = {
            reels: 5,
            rows: 4,
            symbols: ['9', '10', 'J', 'Q', 'K', 'A', '⭐', '🃏'],
            betLevels: [0.10, 0.25, 0.50, 1.00, 2.00, 5.00, 10.00, 25.00, 50.00, 100.00],

            // 📊 PROFESSIONAL RTP MANAGEMENT SYSTEM
            // Multiple RTP tiers based on player behavior and financial patterns
            rtpProfiles: {
                // New players - slightly higher RTP to hook them
                newPlayer: { base: 0.88, variance: 0.03, nearMissRate: 0.20 },

                // Regular players - standard casino RTP
                regular: { base: 0.85, variance: 0.05, nearMissRate: 0.25 },

                // High-value players - balanced experience
                vip: { base: 0.87, variance: 0.04, nearMissRate: 0.22 },

                // Players who withdraw frequently - reduced RTP
                withdrawer: { base: 0.82, variance: 0.06, nearMissRate: 0.30 },

                // Players on winning streaks - temporary reduction
                hotStreak: { base: 0.80, variance: 0.07, nearMissRate: 0.35 },

                // Players on losing streaks - retention boost
                retention: { base: 0.90, variance: 0.02, nearMissRate: 0.15 }
            },

            // 🎯 PROFESSIONAL CASINO REEL STRIPS (Balanced for Engagement + Profit)
            // Designed for frequent small wins with rare but exciting bonuses
            virtualReels: {
                // Standard reels - frequent small wins, rare scatters
                standard: {
                    reel1: [12, 12, 15, 15, 10, 8, 0.3, 2],  // [9, 10, J, Q, K, A, ⭐, 🃏]
                    reel2: [12, 12, 15, 15, 10, 8, 0.3, 2],
                    reel3: [12, 12, 15, 15, 10, 8, 0.2, 2],  // Even rarer scatters on middle reel
                    reel4: [12, 12, 15, 15, 10, 8, 0.3, 2],
                    reel5: [12, 12, 15, 15, 10, 8, 0.3, 2]
                },

                // Small win mode - more frequent low-value matches
                smallWins: {
                    reel1: [15, 15, 18, 18, 8, 6, 0.2, 1.5],
                    reel2: [15, 15, 18, 18, 8, 6, 0.2, 1.5],
                    reel3: [15, 15, 18, 18, 8, 6, 0.1, 1.5],
                    reel4: [15, 15, 18, 18, 8, 6, 0.2, 1.5],
                    reel5: [15, 15, 18, 18, 8, 6, 0.2, 1.5]
                },

                // Generous reels for retention (more medium wins)
                generous: {
                    reel1: [10, 10, 12, 12, 12, 10, 0.5, 3],
                    reel2: [10, 10, 12, 12, 12, 10, 0.5, 3],
                    reel3: [10, 10, 12, 12, 12, 10, 0.3, 3],
                    reel4: [10, 10, 12, 12, 12, 10, 0.5, 3],
                    reel5: [10, 10, 12, 12, 12, 10, 0.5, 3]
                },

                // Tight reels for high-winning players (fewer wins)
                tight: {
                    reel1: [14, 14, 12, 12, 8, 6, 0.1, 1],
                    reel2: [14, 14, 12, 12, 8, 6, 0.1, 1],
                    reel3: [14, 14, 12, 12, 8, 6, 0.05, 1],
                    reel4: [14, 14, 12, 12, 8, 6, 0.1, 1],
                    reel5: [14, 14, 12, 12, 8, 6, 0.1, 1]
                },

                // Bonus hunting mode (slightly higher scatter chance)
                bonusHunt: {
                    reel1: [10, 10, 12, 12, 10, 8, 0.8, 2],
                    reel2: [10, 10, 12, 12, 10, 8, 0.8, 2],
                    reel3: [10, 10, 12, 12, 10, 8, 0.5, 2],  // Still rare on middle reel
                    reel4: [10, 10, 12, 12, 10, 8, 0.8, 2],
                    reel5: [10, 10, 12, 12, 10, 8, 0.8, 2]
                },

                // Free spins reels with premium symbols (no scatters)
                freeSpins: {
                    reel1: [6, 6, 10, 10, 15, 18, 0, 5],
                    reel2: [6, 6, 10, 10, 15, 18, 0, 5],
                    reel3: [6, 6, 10, 10, 15, 18, 0, 5],
                    reel4: [6, 6, 10, 10, 15, 18, 0, 5],
                    reel5: [6, 6, 10, 10, 15, 18, 0, 5]
                }
            },

            // 💰 PROFESSIONAL PAYTABLE (Calibrated for target RTP)
            payouts: {
                '9': [0, 0, 0.2, 0.8, 2.0],     // Low symbols
                '10': [0, 0, 0.3, 1.0, 2.5],
                'J': [0, 0, 0.4, 1.5, 4.0],     // Medium symbols
                'Q': [0, 0, 0.6, 2.0, 6.0],
                'K': [0, 0, 0.8, 3.0, 8.0],     // High symbols
                'A': [0, 0, 1.2, 5.0, 15.0],    // Premium symbol
                '⭐': [0, 0, 0, 0, 0],           // Scatter - triggers bonus
                '🃏': [0, 0, 2.0, 8.0, 25.0]    // Wild - substitute symbol
            },

            // 🎨 PSYCHOLOGICAL COLOR CODING
            colors: {
                '9': '#8B4513', '10': '#CD853F', 'J': '#4169E1', 'Q': '#9932CC',
                'K': '#DC143C', 'A': '#FFD700', '⭐': '#FF1493', '🃏': '#00FF00'
            },

            // 🧠 SMART PLAYER ENGAGEMENT SYSTEM
            psychology: {
                // Small win frequency (keep players engaged)
                smallWinFrequency: 0.35,        // 35% chance for small wins
                smallWinThreshold: 0.8,         // Wins 0.1x to 0.8x bet count as "small"

                // Loss management
                maxConsecutiveLosses: 8,        // Force small win after 8 losses
                maxSpinsWithoutWin: 15,         // Guarantee win after 15 spins

                // Scatter/bonus control
                scatterCooldown: 50,            // Minimum spins between bonuses
                maxSpinsWithoutBonus: 200,      // Force bonus after 200 spins

                // Player retention
                retentionTrigger: 0.30,         // Trigger help at 30% balance
                criticalBalanceTrigger: 0.15,   // Emergency mode at 15% balance

                // Spending-based adjustments
                lowSpenderBonus: 25,            // Bonus spins for small bets
                highSpenderPenalty: 30,         // Penalty spins for big bets

                // Win celebration thresholds
                ldwCelebrationMin: 0.05,        // Celebrate wins > 5% of bet
                bigWinThreshold: 5.0,           // 5x bet = big win

                // Transaction responses
                depositBonusSpins: 20,          // Generous after deposit
                withdrawalPenaltySpins: 40      // Tighter after withdrawal
            },

            // 🎰 VOLATILITY CONTROL
            volatility: {
                low: { bigWinChance: 0.02, mediumWinChance: 0.15, smallWinChance: 0.35 },
                medium: { bigWinChance: 0.05, mediumWinChance: 0.12, smallWinChance: 0.25 },
                high: { bigWinChance: 0.08, mediumWinChance: 0.08, smallWinChance: 0.15 }
            },

            // 🎁 BONUS FEATURES
            bonusFeatures: {
                freeSpinsBase: 10,
                freeSpinsRetrigger: 5,
                maxRetriggers: 1,
                scatterMinCount: 3,
                guaranteedReturnMultiplier: 8.0,
                maxReturnMultiplier: 50.0
            },

            // ⚙️ SYSTEM PARAMETERS
            maxMultiplier: 5,
            autoPlaySpeed: 800,
            animationDuration: 2000,
            soundDelayMs: 200,

            // Add missing properties for compatibility
            freeSpinsBase: 10,
            freeSpinsRetrigger: 5
        };

        let currentBetIndex = 3; // Start at $1.00
        let symbolGrid = [];



        // 🧠 ADVANCED PLAYER BEHAVIOR ANALYSIS & ADAPTIVE RTP ENGINE 🧠
        // Professional casino-grade player tracking and dynamic difficulty adjustment
        const playerAnalytics = {
            // 📊 Core Session Data
            totalSpins: 0,
            totalWagered: 0,
            totalWon: 0,
            netLoss: 0,
            sessionStartBalance: 1000,
            currentSessionRTP: 0,

            // 🎯 Behavioral Patterns
            consecutiveLosses: 0,
            consecutiveWins: 0,
            bigWinCount: 0,
            lastBigWinSpin: -999,
            spinsWithoutWin: 0,
            spinsWithoutScatter: 0,
            nearMissCount: 0,
            ldwCount: 0,

            // 💰 Financial Behavior Analysis
            depositCount: 0,
            withdrawalCount: 0,
            totalDeposited: 0,
            totalWithdrawn: 0,
            withdrawalFrequency: 0, // Withdrawals per session
            depositToWithdrawRatio: 0,
            lastTransactionType: null,
            spinsAfterDeposit: 0,
            spinsAfterWithdrawal: 0,

            // 🎰 Player Classification
            playerType: 'newPlayer', // newPlayer, regular, vip, withdrawer, hotStreak, retention
            riskLevel: 'low', // low, medium, high
            retentionPriority: 'normal', // low, normal, high, critical
            currentRTPProfile: null,

            // 📈 Advanced Analytics
            recentWinHistory: [], // Last 50 spins for pattern analysis
            sessionHistory: [], // Multiple session data
            volatilityPreference: 'medium',
            avgBetSize: 1.0,
            maxBetUsed: 1.0,
            playDuration: 0,

            // 🎮 Game State Tracking
            currentReelSet: 'standard',
            adaptiveMode: false,
            forcedWinPending: false,
            nearMissPending: false,
            retentionBoostActive: false,

            // 🎁 Bonus & Feature Tracking
            freeSpinsTriggered: 0,
            bonusRoundsCompleted: 0,
            averageBonusReturn: 0,
            lastBonusSpin: -999,

            // ⏰ Time-based Analytics
            sessionStartTime: Date.now(),
            lastSpinTime: Date.now(),
            avgSpinInterval: 3000,
            fastPlayDetected: false
        };

        // 🎯 PROFESSIONAL PLAYER CLASSIFICATION SYSTEM
        const playerClassifier = {
            // Analyze player behavior and assign appropriate RTP profile
            classifyPlayer() {
                const analytics = playerAnalytics;
                const user = currentUser;

                if (!user) return 'regular';

                // Calculate key metrics
                const winRate = analytics.totalSpins > 0 ? (analytics.totalWon / analytics.totalWagered) : 0;
                const withdrawalRatio = analytics.totalDeposited > 0 ? (analytics.totalWithdrawn / analytics.totalDeposited) : 0;
                const balanceRatio = user.balance / Math.max(user.stats.totalDeposits, 1000);
                const gamesPlayed = user.stats.gamesPlayed;

                console.log(`🎯 Player Classification Analysis:
                    Win Rate: ${(winRate * 100).toFixed(1)}%
                    Withdrawal Ratio: ${(withdrawalRatio * 100).toFixed(1)}%
                    Balance Ratio: ${(balanceRatio * 100).toFixed(1)}%
                    Games Played: ${gamesPlayed}
                    Withdrawals: ${analytics.withdrawalCount}`);

                // New player detection (first 100 spins)
                if (gamesPlayed < 100) {
                    analytics.playerType = 'newPlayer';
                    analytics.retentionPriority = 'high';
                    console.log('🆕 Classified as NEW PLAYER - Higher RTP for retention');
                    return 'newPlayer';
                }

                // Frequent withdrawer detection
                if (analytics.withdrawalCount >= 3 && withdrawalRatio > 0.8) {
                    analytics.playerType = 'withdrawer';
                    analytics.retentionPriority = 'low';
                    console.log('💸 Classified as WITHDRAWER - Reduced RTP');
                    return 'withdrawer';
                }

                // Hot streak detection (winning too much)
                if (winRate > 1.1 && analytics.bigWinCount >= 3) {
                    analytics.playerType = 'hotStreak';
                    analytics.retentionPriority = 'low';
                    console.log('🔥 Classified as HOT STREAK - Temporary RTP reduction');
                    return 'hotStreak';
                }

                // Retention needed (losing too much)
                if (balanceRatio < 0.3 && analytics.consecutiveLosses > 15) {
                    analytics.playerType = 'retention';
                    analytics.retentionPriority = 'critical';
                    console.log('🆘 Classified as RETENTION - Boosted RTP to prevent churn');
                    return 'retention';
                }

                // VIP player detection (high volume, balanced play)
                if (user.stats.totalWagered > 10000 && withdrawalRatio < 0.6) {
                    analytics.playerType = 'vip';
                    analytics.retentionPriority = 'high';
                    console.log('👑 Classified as VIP - Balanced premium experience');
                    return 'vip';
                }

                // Default to regular player
                analytics.playerType = 'regular';
                analytics.retentionPriority = 'normal';
                console.log('👤 Classified as REGULAR - Standard RTP');
                return 'regular';
            },

            // Update player classification based on recent activity
            updateClassification() {
                const oldType = analytics.playerType;
                const newType = this.classifyPlayer();

                if (oldType !== newType) {
                    console.log(`🔄 Player reclassified: ${oldType} → ${newType}`);
                    this.applyRTPProfile(newType);
                }

                return newType;
            },

            // Apply RTP profile based on player type
            applyRTPProfile(playerType) {
                analytics.currentRTPProfile = config.rtpProfiles[playerType] || config.rtpProfiles.regular;
                console.log(`📊 Applied RTP Profile: ${playerType}`, analytics.currentRTPProfile);
            }
        };

        // 🎰 ADVANCED REEL SELECTION ENGINE
        const reelEngine = {
            // 🎯 SMART REEL SELECTION - Balanced for Engagement & Profit
            selectOptimalReels() {
                const analytics = playerAnalytics;
                const profile = analytics.currentRTPProfile || config.rtpProfiles.regular;

                // Free spins always use premium reels (no scatters)
                if (gameState.inFreeSpins) {
                    analytics.currentReelSet = 'freeSpins';
                    return config.virtualReels.freeSpins;
                }

                // 🎁 SCATTER CONTROL - Make bonuses special and rare
                const spinsSinceBonus = analytics.totalSpins - analytics.lastBonusSpin;
                const shouldPreventBonus = spinsSinceBonus < config.psychology.scatterCooldown;
                const shouldForceBonus = spinsSinceBonus > config.psychology.maxSpinsWithoutBonus;

                if (shouldForceBonus) {
                    analytics.currentReelSet = 'bonusHunt';
                    console.log(`🎁 FORCING BONUS after ${spinsSinceBonus} spins without bonus`);
                    return config.virtualReels.bonusHunt;
                }

                // 💰 SMALL WIN SYSTEM - Keep players engaged with frequent small wins
                const shouldGiveSmallWin = this.shouldTriggerSmallWin();
                if (shouldGiveSmallWin && !shouldPreventBonus) {
                    analytics.currentReelSet = 'smallWins';
                    console.log('💰 Triggering SMALL WIN for player engagement');
                    return config.virtualReels.smallWins;
                }

                // Post-withdrawal penalty period (tighter play)
                if (analytics.spinsAfterWithdrawal < config.psychology.withdrawalPenaltySpins) {
                    analytics.currentReelSet = 'tight';
                    console.log(`🔒 TIGHT reels (${analytics.spinsAfterWithdrawal}/${config.psychology.withdrawalPenaltySpins} penalty spins)`);
                    return config.virtualReels.tight;
                }

                // Post-deposit bonus period (more generous)
                if (analytics.spinsAfterDeposit < config.psychology.depositBonusSpins) {
                    analytics.currentReelSet = 'generous';
                    console.log(`💰 GENEROUS reels (${analytics.spinsAfterDeposit}/${config.psychology.depositBonusSpins} bonus spins)`);
                    return config.virtualReels.generous;
                }

                // Emergency retention mode for critical balance
                if (analytics.retentionBoostActive || analytics.playerType === 'retention') {
                    analytics.currentReelSet = 'generous';
                    console.log('🆘 GENEROUS reels for player retention');
                    return config.virtualReels.generous;
                }

                // Hot streak players get tighter reels (house protection)
                if (analytics.playerType === 'hotStreak' || analytics.playerType === 'withdrawer') {
                    analytics.currentReelSet = 'tight';
                    console.log('🔥 TIGHT reels for hot streak/withdrawer');
                    return config.virtualReels.tight;
                }

                // Default to standard reels with balanced small win frequency
                analytics.currentReelSet = 'standard';
                return config.virtualReels.standard;
            },

            // 💰 SMART SMALL WIN TRIGGER SYSTEM
            shouldTriggerSmallWin() {
                const analytics = playerAnalytics;
                const user = currentUser;

                // Force small win after too many losses
                if (analytics.spinsWithoutWin >= config.psychology.maxSpinsWithoutWin) {
                    console.log(`💰 FORCING small win after ${analytics.spinsWithoutWin} spins without win`);
                    return true;
                }

                // Higher chance for small wins when balance is low
                if (user && user.balance < (gameState.currentBet * 20)) {
                    if (Math.random() < config.psychology.smallWinFrequency * 1.5) {
                        console.log('💰 Higher small win chance due to low balance');
                        return true;
                    }
                }

                // Regular small win frequency
                if (Math.random() < config.psychology.smallWinFrequency) {
                    console.log('💰 Regular small win trigger');
                    return true;
                }

                // Bonus chance for new players
                if (analytics.playerType === 'newPlayer' && Math.random() < 0.15) {
                    console.log('💰 New player small win bonus');
                    return true;
                }

                return false;
            },

            // Generate symbol for specific reel using weighted selection
            generateSymbol(reelIndex, reelWeights) {
                const weights = reelWeights[`reel${reelIndex + 1}`] || reelWeights.reel1;
                const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
                let random = Math.random() * totalWeight;

                for (let i = 0; i < config.symbols.length; i++) {
                    random -= weights[i];
                    if (random <= 0) {
                        return config.symbols[i];
                    }
                }

                return config.symbols[0]; // Fallback
            }
        };

        // 🧠 PSYCHOLOGICAL ENGAGEMENT ENGINE
        const psychologyEngine = {
            // Determine if this should be a forced win for player retention
            shouldForceWin() {
                const analytics = playerAnalytics;

                // Force win after too many consecutive losses
                if (analytics.consecutiveLosses >= config.psychology.maxConsecutiveLosses) {
                    console.log(`🎯 FORCING WIN after ${analytics.consecutiveLosses} consecutive losses`);
                    return true;
                }

                // Force win for retention players
                if (analytics.retentionBoostActive && Math.random() < 0.4) {
                    console.log('🆘 FORCING WIN for player retention');
                    return true;
                }

                // Force win after deposit to encourage continued play
                if (analytics.spinsAfterDeposit < 10 && Math.random() < 0.3) {
                    console.log('💰 FORCING WIN after recent deposit');
                    return true;
                }

                return false;
            },

            // Determine if this should be a near-miss for psychological engagement
            shouldCreateNearMiss() {
                const analytics = playerAnalytics;
                const profile = analytics.currentRTPProfile || config.rtpProfiles.regular;

                // Higher near-miss rate for certain player types
                if (analytics.playerType === 'withdrawer' || analytics.playerType === 'hotStreak') {
                    return Math.random() < (profile.nearMissRate * 1.5);
                }

                return Math.random() < profile.nearMissRate;
            },

            // Check if win should be celebrated as Loss-Disguised-as-Win
            isLossDisguisedAsWin(winAmount, betAmount) {
                return winAmount > 0 && winAmount < betAmount && winAmount >= (betAmount * config.psychology.ldwCelebrationMin);
            },

            // Trigger retention boost for struggling players
            checkRetentionTrigger() {
                const analytics = playerAnalytics;
                const user = currentUser;

                if (!user) return;

                const balanceRatio = user.balance / Math.max(user.stats.totalDeposits, 1000);

                if (balanceRatio < config.psychology.retentionTrigger && !analytics.retentionBoostActive) {
                    analytics.retentionBoostActive = true;
                    console.log('🆘 RETENTION BOOST ACTIVATED - Player balance critically low');

                    // Show encouraging message
                    setTimeout(() => {
                        showMessage('🍀 Your luck is about to change! Keep spinning!', 'info');
                    }, 1000);
                }
            },

            // Update player analytics after each spin
            updateAnalytics(spinResult) {
                const analytics = playerAnalytics;
                const now = Date.now();

                // Update basic counters
                analytics.totalSpins++;
                analytics.lastSpinTime = now;
                analytics.spinsAfterDeposit++;
                analytics.spinsAfterWithdrawal++;

                // Track win/loss patterns
                analytics.recentWinHistory.push({
                    spin: analytics.totalSpins,
                    win: spinResult.totalWin,
                    bet: gameState.currentBet,
                    timestamp: now
                });

                // Keep only last 50 spins
                if (analytics.recentWinHistory.length > 50) {
                    analytics.recentWinHistory.shift();
                }

                // Update consecutive counters
                if (spinResult.totalWin > 0) {
                    analytics.consecutiveLosses = 0;
                    analytics.consecutiveWins++;
                    analytics.spinsWithoutWin = 0;

                    // Track big wins
                    if (spinResult.totalWin >= gameState.currentBet * 5) {
                        analytics.bigWinCount++;
                        analytics.lastBigWinSpin = analytics.totalSpins;
                    }
                } else {
                    analytics.consecutiveLosses++;
                    analytics.consecutiveWins = 0;
                    analytics.spinsWithoutWin++;
                }

                // Update scatter tracking
                if (spinResult.scatterCount < 3) {
                    analytics.spinsWithoutScatter++;
                } else {
                    analytics.spinsWithoutScatter = 0;
                    analytics.freeSpinsTriggered++;
                    analytics.lastBonusSpin = analytics.totalSpins;
                }

                // Calculate session RTP
                if (analytics.totalWagered > 0) {
                    analytics.currentSessionRTP = analytics.totalWon / analytics.totalWagered;
                }

                // Update player classification periodically
                if (analytics.totalSpins % 20 === 0) {
                    playerClassifier.updateClassification();
                }

                // Check retention triggers
                this.checkRetentionTrigger();

                console.log(`📊 Analytics Update - Spins: ${analytics.totalSpins}, RTP: ${(analytics.currentSessionRTP * 100).toFixed(1)}%, Type: ${analytics.playerType}`);
            }
        };

        // 💰 TRANSACTION TRACKING INTEGRATION
        const transactionTracker = {
            // Track deposit and update analytics
            trackDeposit(amount) {
                const analytics = playerAnalytics;

                analytics.depositCount++;
                analytics.totalDeposited += amount;
                analytics.lastTransactionType = 'deposit';
                analytics.spinsAfterDeposit = 0;

                // Activate deposit bonus period
                console.log(`💰 DEPOSIT TRACKED: $${amount} - Activating bonus period`);

                // Update player classification
                playerClassifier.updateClassification();
            },

            // Track withdrawal and update analytics
            trackWithdrawal(amount) {
                const analytics = playerAnalytics;

                analytics.withdrawalCount++;
                analytics.totalWithdrawn += amount;
                analytics.lastTransactionType = 'withdrawal';
                analytics.spinsAfterWithdrawal = 0;

                // Calculate withdrawal frequency
                analytics.withdrawalFrequency = analytics.withdrawalCount / Math.max(analytics.depositCount, 1);
                analytics.depositToWithdrawRatio = analytics.totalWithdrawn / Math.max(analytics.totalDeposited, 1);

                // Activate withdrawal penalty period
                console.log(`💸 WITHDRAWAL TRACKED: $${amount} - Activating penalty period`);

                // Update player classification
                playerClassifier.updateClassification();
            }
        };

        // 📊 UPDATE ANALYTICS DISPLAY
        function updateAnalyticsDisplay() {
            const analytics = playerAnalytics;
            const spinsSinceBonus = analytics.totalSpins - analytics.lastBonusSpin;
            const cooldownRemaining = Math.max(0, config.psychology.scatterCooldown - spinsSinceBonus);

            // Update all analytics values
            document.getElementById('analytics-spins').textContent = analytics.totalSpins;
            document.getElementById('analytics-rtp').textContent = (analytics.currentSessionRTP * 100).toFixed(1) + '%';
            document.getElementById('analytics-type').textContent = analytics.playerType;
            document.getElementById('analytics-reels').textContent = analytics.currentReelSet;
            document.getElementById('analytics-losses').textContent = analytics.consecutiveLosses;
            document.getElementById('analytics-nowins').textContent = analytics.spinsWithoutWin;
            document.getElementById('analytics-nobonus').textContent = spinsSinceBonus;
            document.getElementById('analytics-cooldown').textContent = cooldownRemaining;

            // Color coding based on values
            const rtpElement = document.getElementById('analytics-rtp');
            if (analytics.currentSessionRTP > 0.9) {
                rtpElement.style.color = '#FF4444'; // Red for high RTP
            } else if (analytics.currentSessionRTP > 0.85) {
                rtpElement.style.color = '#FFAA00'; // Orange for medium RTP
            } else {
                rtpElement.style.color = '#00FF00'; // Green for low RTP
            }

            // Highlight critical values
            const lossElement = document.getElementById('analytics-losses');
            if (analytics.consecutiveLosses >= config.psychology.maxConsecutiveLosses - 2) {
                lossElement.style.color = '#FF0000';
                lossElement.style.fontWeight = 'bold';
            } else {
                lossElement.style.color = '#FF4444';
                lossElement.style.fontWeight = 'normal';
            }

            const noWinElement = document.getElementById('analytics-nowins');
            if (analytics.spinsWithoutWin >= config.psychology.maxSpinsWithoutWin - 3) {
                noWinElement.style.color = '#FF0000';
                noWinElement.style.fontWeight = 'bold';
            } else {
                noWinElement.style.color = '#FF4444';
                noWinElement.style.fontWeight = 'normal';
            }
        }

        // Simple fallback functions for compatibility
        function getRandomSymbol() {
            const symbols = config.symbols;
            return symbols[Math.floor(Math.random() * symbols.length)];
        }

        // 🔧 SIMPLE FALLBACK FUNCTIONS FOR IMMEDIATE FUNCTIONALITY
        function generateWeightedGridFallback() {
            console.log('🎰 Using fallback grid generation...');
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    const symbol = getRandomSymbol();
                    symbolGrid[reel][row] = symbol;
                    updateSymbolDisplay(reel, row, symbol);
                }
            }
        }

        function checkForWinsAdvancedFallback() {
            console.log('🎰 Using fallback win checking...');
            const wins = [];
            let totalWin = 0;
            let scatterCount = 0;

            // Count scatters
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    if (symbolGrid[reel][row] === '⭐') {
                        scatterCount++;
                    }
                }
            }

            // Check horizontal lines for wins
            for (let row = 0; row < config.rows; row++) {
                let consecutiveCount = 1;
                let currentSymbol = symbolGrid[0][row];

                if (!currentSymbol || currentSymbol === '⭐') continue;

                for (let reel = 1; reel < config.reels; reel++) {
                    if (symbolGrid[reel][row] === currentSymbol || symbolGrid[reel][row] === '🃏') {
                        consecutiveCount++;
                    } else {
                        break;
                    }
                }

                if (consecutiveCount >= 3) {
                    const payout = config.payouts[currentSymbol];
                    if (payout && payout[consecutiveCount - 1] > 0) {
                        const winAmount = payout[consecutiveCount - 1] * gameState.currentBet;
                        totalWin += winAmount;
                        wins.push({
                            symbol: currentSymbol,
                            count: consecutiveCount,
                            amount: winAmount,
                            row: row
                        });
                    }
                }
            }

            return {
                totalWin: totalWin,
                scatterCount: scatterCount,
                wins: wins,
                isLoss: totalWin === 0
            };
        }

        // 🎯 PROFESSIONAL SYMBOL GENERATION WITH CASINO PSYCHOLOGY
        function generateProfessionalGrid() {
            console.log('🎯 Generating professional casino grid...');

            // 🧠 DETERMINE GAME STATE AND PSYCHOLOGY
            const shouldForceWin = playerAnalytics.consecutiveLosses >= 6;
            const shouldCreateNearMiss = !shouldForceWin && Math.random() < 0.15; // 15% near miss rate
            const isRetentionMode = playerAnalytics.consecutiveLosses >= 4;

            // 🎰 WEIGHTED SYMBOL DISTRIBUTION (Professional Casino Style)
            const symbolWeights = {
                // Low symbols - frequent but low payout
                '9': gameState.inFreeSpins ? 8 : 15,
                '10': gameState.inFreeSpins ? 8 : 15,
                'J': gameState.inFreeSpins ? 10 : 12,
                'Q': gameState.inFreeSpins ? 10 : 12,
                // High symbols - less frequent but better payout
                'K': gameState.inFreeSpins ? 12 : 8,
                'A': gameState.inFreeSpins ? 15 : 6,
                // Special symbols
                '⭐': gameState.inFreeSpins ? 0 : (isRetentionMode ? 1.5 : 0.5), // More scatters when player struggling
                '🃏': gameState.inFreeSpins ? 3 : 1.5 // Wild symbol
            };

            // Generate grid with weighted distribution
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    const symbol = getWeightedSymbol(symbolWeights);
                    symbolGrid[reel][row] = symbol;
                    updateSymbolDisplay(reel, row, symbol);
                }
            }

            // 🎭 APPLY PSYCHOLOGICAL PATTERNS
            if (shouldForceWin) {
                console.log('🎯 FORCING WIN for player retention');
                forceSmallWinPattern();
            } else if (shouldCreateNearMiss) {
                console.log('🎭 Creating NEAR MISS for psychological engagement');
                createNearMissPattern();
            }

            // 🎁 SCATTER CONTROL (Professional frequency management)
            controlScatterFrequency();
        }

        // 🎲 WEIGHTED SYMBOL SELECTION
        function getWeightedSymbol(weights) {
            const symbols = Object.keys(weights);
            const totalWeight = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
            let random = Math.random() * totalWeight;

            for (const symbol of symbols) {
                random -= weights[symbol];
                if (random <= 0) {
                    return symbol;
                }
            }

            return symbols[0]; // Fallback
        }

        // 🎯 FORCE SMALL WIN PATTERN (Retention Psychology)
        function forceSmallWinPattern() {
            const winSymbols = ['J', 'Q', 'K'];
            const winSymbol = winSymbols[Math.floor(Math.random() * winSymbols.length)];
            const winRow = Math.floor(Math.random() * config.rows);

            // Place 3 matching symbols for guaranteed small win
            for (let reel = 0; reel < 3; reel++) {
                symbolGrid[reel][winRow] = winSymbol;
                updateSymbolDisplay(reel, winRow, winSymbol);
            }

            console.log(`💰 Forced retention win: 3x ${winSymbol} on row ${winRow + 1}`);
        }

        // 🎭 CREATE NEAR MISS PATTERN (Psychological Engagement)
        function createNearMissPattern() {
            const nearMissSymbols = ['A', 'K', 'Q'];
            const nearMissSymbol = nearMissSymbols[Math.floor(Math.random() * nearMissSymbols.length)];
            const nearMissRow = Math.floor(Math.random() * config.rows);

            // Place 2 matching symbols (almost a win)
            symbolGrid[0][nearMissRow] = nearMissSymbol;
            symbolGrid[1][nearMissRow] = nearMissSymbol;
            updateSymbolDisplay(0, nearMissRow, nearMissSymbol);
            updateSymbolDisplay(1, nearMissRow, nearMissSymbol);

            console.log(`🎭 Near miss created: 2x ${nearMissSymbol} on row ${nearMissRow + 1}`);
        }

        // 🎁 PROFESSIONAL SCATTER FREQUENCY CONTROL
        function controlScatterFrequency() {
            const scatterCount = countScattersInGrid();
            const spinsWithoutBonus = playerAnalytics.totalSpins - (playerAnalytics.lastBonusSpin || 0);

            // Force scatter bonus if player hasn't had one in too long
            if (spinsWithoutBonus > 150 && scatterCount < 3) {
                console.log('🎁 FORCING SCATTER BONUS - Player needs retention');
                forceScatterBonus();
            }
            // Prevent too frequent bonuses (cooling period)
            else if (spinsWithoutBonus < 20 && scatterCount >= 3) {
                console.log('🧊 COOLING SCATTER BONUS - Too frequent');
                removeExcessScatters();
            }
        }

        // 🎁 FORCE SCATTER BONUS
        function forceScatterBonus() {
            const scatterPositions = [
                {reel: 0, row: Math.floor(Math.random() * config.rows)},
                {reel: 2, row: Math.floor(Math.random() * config.rows)},
                {reel: 4, row: Math.floor(Math.random() * config.rows)}
            ];

            scatterPositions.forEach(pos => {
                symbolGrid[pos.reel][pos.row] = '⭐';
                updateSymbolDisplay(pos.reel, pos.row, '⭐');
            });

            playerAnalytics.lastBonusSpin = playerAnalytics.totalSpins;
        }

        // 🧊 REMOVE EXCESS SCATTERS
        function removeExcessScatters() {
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    if (symbolGrid[reel][row] === '⭐') {
                        symbolGrid[reel][row] = getWeightedSymbol({'9': 1, '10': 1, 'J': 1});
                        updateSymbolDisplay(reel, row, symbolGrid[reel][row]);
                        break; // Remove only one
                    }
                }
            }
        }

        // 🔢 COUNT SCATTERS IN GRID
        function countScattersInGrid() {
            let count = 0;
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    if (symbolGrid[reel][row] === '⭐') {
                        count++;
                    }
                }
            }
            return count;
        }

        // 🏆 PROFESSIONAL WIN CHECKING SYSTEM
        function checkProfessionalWins() {
            console.log('🏆 Checking for professional wins...');

            let totalWin = 0;
            let scatterCount = 0;
            let wildCount = 0;
            const wins = [];

            // Count special symbols
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    if (symbolGrid[reel][row] === '⭐') scatterCount++;
                    if (symbolGrid[reel][row] === '🃏') wildCount++;
                }
            }

            // Check horizontal paylines (4 rows)
            for (let row = 0; row < config.rows; row++) {
                const lineWin = checkPaylineWin(row);
                if (lineWin.win > 0) {
                    wins.push(lineWin);
                    totalWin += lineWin.win;
                    console.log(`💰 Line ${row + 1}: ${lineWin.count}x ${lineWin.symbol} = $${lineWin.win.toFixed(2)}`);
                }
            }

            // 🎁 FREQUENT SMALL WINS SYSTEM (Casino Psychology)
            if (totalWin === 0 && Math.random() < 0.25) { // 25% chance for small consolation win
                const consolationWin = createConsolationWin();
                if (consolationWin > 0) {
                    totalWin += consolationWin;
                    console.log(`🎭 Consolation win: $${consolationWin.toFixed(2)}`);
                }
            }

            return {
                totalWin: totalWin,
                scatterCount: scatterCount,
                wildCount: wildCount,
                wins: wins,
                isLoss: totalWin === 0
            };
        }

        // 💰 CHECK INDIVIDUAL PAYLINE
        function checkPaylineWin(row) {
            let consecutiveCount = 1;
            let currentSymbol = symbolGrid[0][row];

            // Skip scatters for payline wins
            if (!currentSymbol || currentSymbol === '⭐') {
                return { win: 0, symbol: null, count: 0 };
            }

            // Count consecutive symbols (including wilds)
            for (let reel = 1; reel < config.reels; reel++) {
                const symbol = symbolGrid[reel][row];
                if (symbol === currentSymbol || symbol === '🃏' || currentSymbol === '🃏') {
                    consecutiveCount++;
                    if (currentSymbol === '🃏' && symbol !== '🃏') {
                        currentSymbol = symbol; // Wild takes on the symbol
                    }
                } else {
                    break;
                }
            }

            // Calculate payout
            if (consecutiveCount >= 3) {
                const payout = config.payouts[currentSymbol];
                if (payout && payout[consecutiveCount - 1] > 0) {
                    const winAmount = payout[consecutiveCount - 1] * gameState.currentBet;
                    return {
                        win: winAmount,
                        symbol: currentSymbol,
                        count: consecutiveCount,
                        row: row
                    };
                }
            }

            return { win: 0, symbol: null, count: 0 };
        }

        // 🎭 CREATE CONSOLATION WIN (Psychology)
        function createConsolationWin() {
            // Small random win to keep player engaged
            const consolationAmount = gameState.currentBet * (0.1 + Math.random() * 0.3); // 10-40% of bet
            return consolationAmount;
        }

        // 🎁 PROFESSIONAL FREE SPINS SYSTEM
        function triggerProfessionalFreeSpins(scatterCount) {
            console.log(`🎁 Professional free spins triggered with ${scatterCount} scatters`);

            // Determine free spins amount
            let freeSpinsAwarded = 10; // Default for 3 scatters
            if (scatterCount === 4) freeSpinsAwarded = 15;
            if (scatterCount >= 5) freeSpinsAwarded = 25;

            gameState.inFreeSpins = true;
            gameState.freeSpinsRemaining = freeSpinsAwarded;
            gameState.freeSpinsTotalWin = 0;

            // 💰 PROFESSIONAL FREE SPINS TARGET (30-50% return of total bet)
            const targetReturnMultiplier = 0.3 + (Math.random() * 0.2); // 30-50%
            gameState.freeSpinsTargetReturn = gameState.currentBet * freeSpinsAwarded * targetReturnMultiplier;

            // Show free spins display
            showElement('freespins-display');

            // Play scatter sound
            if (audioSystem && audioSystem.playScatterSound) {
                audioSystem.playScatterSound();
            }

            // Update analytics
            if (!playerAnalytics.lastBonusSpin) playerAnalytics.lastBonusSpin = 0;
            playerAnalytics.lastBonusSpin = playerAnalytics.totalSpins;

            showMessage(`🎊 FREE SPINS! ${freeSpinsAwarded} spins awarded!`);
            console.log(`✅ Free spins triggered: ${freeSpinsAwarded} spins, target return: $${gameState.freeSpinsTargetReturn.toFixed(2)}`);
        }

        // 🆘 FORCE RETENTION WIN
        function forceRetentionWin() {
            console.log('🆘 FORCING RETENTION WIN - Player needs help');

            // Reset consecutive losses
            playerAnalytics.consecutiveLosses = 0;

            // Force a guaranteed win on next spin
            gameState.isSpinning = false;

            // Create a guaranteed winning pattern
            const winSymbol = ['Q', 'K', 'A'][Math.floor(Math.random() * 3)];
            const winRow = Math.floor(Math.random() * config.rows);

            // Place 4 matching symbols for a good win
            for (let reel = 0; reel < 4; reel++) {
                symbolGrid[reel][winRow] = winSymbol;
                updateSymbolDisplay(reel, winRow, winSymbol);
            }

            // Calculate and award the win
            const payout = config.payouts[winSymbol];
            const winAmount = payout[3] * gameState.currentBet; // 4-symbol payout

            gameState.lastWin = winAmount;
            gameState.balance += winAmount;
            playerAnalytics.totalWon += winAmount;

            if (currentUser) {
                currentUser.balance = gameState.balance;
            }

            showWinCelebration(winAmount, 'RETENTION');
            updateDisplay();

            console.log(`🆘 Retention win awarded: $${winAmount.toFixed(2)} (4x ${winSymbol})`);
        }

        // 🎵 SIMPLE HTML5 AUDIO SYSTEM 🎵
        // ✅ Guaranteed to work in all browsers
        const audioSystem = {
            // Get audio elements
            bgMusic: document.getElementById('bgMusic'),
            spinSound: document.getElementById('spinSound'),
            winSound: document.getElementById('winSound'),
            musicEnabled: true,
            isInitialized: false,

            // Simple initialization
            async init() {
                console.log('🎵 Initializing Simple Audio System...');
                this.isInitialized = true;

                // Set volumes
                if (this.bgMusic) this.bgMusic.volume = 0.3;
                if (this.spinSound) this.spinSound.volume = 0.7;
                if (this.winSound) this.winSound.volume = 0.8;

                console.log('✅ Simple Audio System Ready!');
            },

            // Play background music
            startBackgroundMusic() {
                if (!this.musicEnabled || !this.bgMusic) return;
                try {
                    this.bgMusic.currentTime = 0;
                    this.bgMusic.play().then(() => {
                        console.log('🎼 ✅ Background music playing!');
                    }).catch(e => {
                        console.log('🎼 ❌ Background music failed:', e);
                    });
                } catch (e) {
                    console.log('🎼 ❌ Background music error:', e);
                }
            },

            // Stop background music
            stopBackgroundMusic() {
                if (this.bgMusic) {
                    this.bgMusic.pause();
                    this.bgMusic.currentTime = 0;
                    console.log('🎼 Background music stopped');
                }
            },

            // Play spin sound
            playSpinButtonSound() {
                if (!this.musicEnabled || !this.spinSound) return;
                try {
                    this.spinSound.currentTime = 0;
                    this.spinSound.play().then(() => {
                        console.log('🎛️ ✅ Spin sound playing!');
                    }).catch(e => {
                        console.log('🎛️ ❌ Spin sound failed:', e);
                    });
                } catch (e) {
                    console.log('🎛️ ❌ Spin sound error:', e);
                }
            },

            // Play win sound
            playWinSound(winAmount, betAmount, multiplier = 1) {
                if (!this.musicEnabled || !this.winSound) return;
                try {
                    this.winSound.currentTime = 0;
                    this.winSound.play().then(() => {
                        console.log('🎰 ✅ Win sound playing!');
                    }).catch(e => {
                        console.log('🎰 ❌ Win sound failed:', e);
                    });
                } catch (e) {
                    console.log('🎰 ❌ Win sound error:', e);
                }
            },

            // Toggle music
            toggleMusic() {
                this.musicEnabled = !this.musicEnabled;
                if (this.musicEnabled) {
                    this.startBackgroundMusic();
                    console.log('🎵 MUSIC ENABLED!');
                    } else {
                    this.stopBackgroundMusic();
                    console.log('🔇 MUSIC DISABLED!');
                }
                return this.musicEnabled;
            },

            // Dummy methods for compatibility
            playReelSpinSound() { this.playSpinButtonSound(); },
            stopReelSpinSound() { },
            playMultiplierSound() { this.playWinSound(); },
            playCheerSound() { this.playWinSound(); },
            playCoinDropSound() { this.playWinSound(); },
            playBonusSound() { this.playWinSound(); },
            playFreeSpinSound() { this.playWinSound(); },
            playScatterSound() { this.playWinSound(); },
            playCardFlipSound() { this.playSpinButtonSound(); },
            playCardMatchSound() { this.playWinSound(); },
            playCardComboSound() { this.playWinSound(); },
            playAnticipation() { this.playSpinButtonSound(); },
            playModernWin() { this.playWinSound(); }
        };

        // Initialize Game with Audio System
        async function initGame() {
            console.log('🎮 Initializing Vegas Ace Slots...');

            // Initialize audio system
            await audioSystem.init();

            // Sync game state with user balance
            if (currentUser) {
                gameState.balance = currentUser.balance;
                playerAnalytics.sessionStartBalance = currentUser.balance;
                console.log('🎯 Analytics initialized from user data');
            }

            console.log('🎰 Creating reels...');
            createReels();

            console.log('🎰 Populating reels...');
            populateReels();

            console.log('🎰 Setting up event listeners...');
            setupEventListeners();

            console.log('🎰 Updating display...');
            updateDisplay();

            console.log('✅ Game initialized successfully! Click SPIN to enable audio.');
        }

        // 🎮 SPECIAL INITIALIZATION AFTER LOGIN
        async function initGameAfterLogin() {
            console.log('🎮 Initializing game after login...');

            try {
                // Initialize audio system
                if (audioSystem && audioSystem.init) {
                    await audioSystem.init();
                }

                // Sync game state with user balance
                if (currentUser) {
                    gameState.balance = currentUser.balance;
                    if (playerAnalytics) {
                        playerAnalytics.sessionStartBalance = currentUser.balance;
                    }
                    console.log('🎯 User balance synced:', gameState.balance);
                }

                console.log('🎰 Creating reels...');
                createReels();

                console.log('🎰 Populating reels...');
                populateReels();

                console.log('🎰 Setting up event listeners...');
                setupEventListeners();

                console.log('🎰 Updating display...');
                updateDisplay();

                // Update user profile display
                updateUserProfile();

                console.log('✅ Game initialized successfully after login!');
            } catch (error) {
                console.error('❌ Error initializing game:', error);
            }
        }

        // 👤 UPDATE USER PROFILE DISPLAY
        function updateUserProfile() {
            if (!currentUser) return;

            console.log('👤 Updating user profile display...');

            // Update any user-specific UI elements
            const userNameElements = document.querySelectorAll('.user-name');
            userNameElements.forEach(el => {
                if (el) el.textContent = currentUser.name;
            });

            const userBalanceElements = document.querySelectorAll('.user-balance');
            userBalanceElements.forEach(el => {
                if (el) el.textContent = `$${currentUser.balance.toFixed(2)}`;
            });
        }

        // Add event listener to enable audio on first click
        document.addEventListener('click', function enableAudio() {
            if (audioSystem.musicEnabled && audioSystem.bgMusic) {
                audioSystem.startBackgroundMusic();
                console.log('🎵 Audio enabled on first click!');
            }
            // Remove this listener after first click
            document.removeEventListener('click', enableAudio);
        }, { once: true });

        function createReels() {
            console.log('🎰 Creating reels...');
            const reelsContainer = document.getElementById('reels');

            if (!reelsContainer) {
                console.error('❌ Reels container not found!');
                return;
            }

            reelsContainer.innerHTML = '';
            symbolGrid = [];

            for (let reel = 0; reel < config.reels; reel++) {
                symbolGrid[reel] = [];
                for (let row = 0; row < config.rows; row++) {
                    const symbolElement = document.createElement('div');
                    symbolElement.className = 'symbol';
                    symbolElement.id = `symbol-${reel}-${row}`;
                    symbolElement.textContent = '?'; // Placeholder
                    reelsContainer.appendChild(symbolElement);
                    symbolGrid[reel][row] = null;
                    console.log(`Created symbol element: symbol-${reel}-${row}`);
                }
            }
            console.log('✅ Reels created successfully');
        }

        function populateReels() {
            console.log('🎰 Populating reels with symbols...');
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    const symbol = getRandomSymbol();
                    symbolGrid[reel][row] = symbol;
                    updateSymbolDisplay(reel, row, symbol);
                }
            }
            console.log('✅ Reels populated with symbols');
        }

        // Simple random symbol generation for now
        function getRandomSymbolAdvanced(isScatterAllowed = true, forceNearMiss = false) {
            const symbols = config.symbols;
            if (!isScatterAllowed) {
                return symbols.filter(s => s !== '⭐')[Math.floor(Math.random() * (symbols.length - 1))];
            }
            return symbols[Math.floor(Math.random() * symbols.length)];
        }

        function updateSymbolDisplay(reel, row, symbol) {
            const element = document.getElementById(`symbol-${reel}-${row}`);
            if (element) {
                element.textContent = symbol;
                element.style.color = config.colors[symbol] || '#FFD700';
            }
        }

        function setupEventListeners() {
            console.log('🎮 Setting up event listeners...');

            // Spin button
            const spinButton = document.getElementById('spin-button');
            if (spinButton) {
                spinButton.addEventListener('click', () => {
                    console.log('Spin button clicked');
                    spin();
                });
                console.log('✅ Spin button listener added');
            } else {
                console.error('❌ Spin button not found!');
            }

            // Bet controls
            const betDownButton = document.getElementById('bet-down');
            if (betDownButton) {
                betDownButton.addEventListener('click', () => {
                    console.log('Bet down clicked');
                    decreaseBet();
                });
            }

            const betUpButton = document.getElementById('bet-up');
            if (betUpButton) {
                betUpButton.addEventListener('click', () => {
                    console.log('Bet up clicked');
                    increaseBet();
                });
            }

            const maxBetButton = document.getElementById('max-bet');
            if (maxBetButton) {
                maxBetButton.addEventListener('click', () => {
                    console.log('Max bet clicked');
                    setMaxBet();
                });
            }

            const musicToggle = document.getElementById('music-toggle');
            if (musicToggle) {
                musicToggle.addEventListener('click', () => {
                    console.log('Music toggle clicked');
                    toggleMusic();
                });
            }

            // Keyboard controls
            document.addEventListener('keydown', (event) => {
                if (event.code === 'Space') {
                    event.preventDefault();
                    spin();
                }
            });

            console.log('✅ Event listeners set up successfully');
        }

        // 🎰 PROFESSIONAL CASINO SPIN FUNCTION WITH NATURAL PSYCHOLOGY
        async function spin() {
            console.log('🎰 Professional casino spin initiated');

            if (gameState.isSpinning) {
                console.log('Already spinning, ignoring');
                return;
            }

            if (gameState.balance < gameState.currentBet && !gameState.inFreeSpins) {
                showMessage('Insufficient balance!');
                return;
            }

            gameState.isSpinning = true;
            gameState.lastWin = 0;

            // Update spin button
            updateSpinButtonState();

            // Play spin sound
            if (audioSystem && audioSystem.playSpinButtonSound) {
                audioSystem.playSpinButtonSound();
            }

            // 📊 TRACK PLAYER BEHAVIOR
            if (!playerAnalytics.totalSpins) playerAnalytics.totalSpins = 0;
            if (!playerAnalytics.spinsWithoutWin) playerAnalytics.spinsWithoutWin = 0;
            if (!playerAnalytics.consecutiveLosses) playerAnalytics.consecutiveLosses = 0;

            playerAnalytics.totalSpins++;
            playerAnalytics.spinsWithoutWin++;

            // Deduct bet and track
            if (!gameState.inFreeSpins) {
                gameState.balance -= gameState.currentBet;
                if (!playerAnalytics.totalWagered) playerAnalytics.totalWagered = 0;
                if (!playerAnalytics.totalWon) playerAnalytics.totalWon = 0;
                playerAnalytics.totalWagered += gameState.currentBet;

                console.log(`💰 Bet deducted: $${gameState.currentBet.toFixed(2)}, Balance: $${gameState.balance.toFixed(2)}`);
            } else {
                gameState.freeSpinsRemaining--;
                console.log(`🎁 Free spin ${gameState.freeSpinsRemaining} remaining`);
            }

            // Update display
            updateDisplay();

            // Animate spinning
            await animateSpinning();

            // 🎯 PROFESSIONAL SYMBOL GENERATION WITH PSYCHOLOGY
            generateProfessionalGrid();

            // Check for wins with advanced system
            const spinResult = checkProfessionalWins();

            // 💰 PROCESS WINS WITH CASINO PSYCHOLOGY
            if (spinResult.totalWin > 0) {
                gameState.lastWin = spinResult.totalWin;
                gameState.balance += spinResult.totalWin;
                playerAnalytics.totalWon += spinResult.totalWin;
                playerAnalytics.spinsWithoutWin = 0;
                playerAnalytics.consecutiveLosses = 0;

                if (gameState.inFreeSpins) {
                    gameState.freeSpinsTotalWin += spinResult.totalWin;
                }

                // Update user balance
                if (currentUser) {
                    currentUser.balance = gameState.balance;
                }

                // 🎭 LOSS DISGUISED AS WIN PSYCHOLOGY
                const isLDW = spinResult.totalWin < gameState.currentBet;
                if (isLDW) {
                    showWinCelebration(spinResult.totalWin, 'LDW');
                    console.log(`🎭 LDW: Won $${spinResult.totalWin.toFixed(2)} on $${gameState.currentBet.toFixed(2)} bet (feels like win, actually loss)`);
                } else {
                    showWinCelebration(spinResult.totalWin, 'WIN');
                    console.log(`💰 TRUE WIN: $${spinResult.totalWin.toFixed(2)}`);
                }

                // Play win sound
                if (audioSystem && audioSystem.playWinSound) {
                    audioSystem.playWinSound(spinResult.totalWin, gameState.currentBet);
                }
            } else {
                // Handle losses with retention psychology
                playerAnalytics.consecutiveLosses++;
                console.log(`💸 Loss (${playerAnalytics.consecutiveLosses} consecutive)`);

                // 🆘 RETENTION SYSTEM - Force win after too many losses
                if (playerAnalytics.consecutiveLosses >= 8) {
                    console.log('🆘 RETENTION TRIGGER: Forcing win after 8 consecutive losses');
                    forceRetentionWin();
                    return; // Exit and re-run with forced win
                }
            }

            // 🎁 SCATTER BONUS SYSTEM
            if (spinResult.scatterCount >= 3) {
                triggerProfessionalFreeSpins(spinResult.scatterCount);
            }

            // Reset spinning state
            gameState.isSpinning = false;
            updateSpinButtonState();
            updateDisplay();

            console.log(`✅ Spin #${playerAnalytics.totalSpins} completed - Balance: $${gameState.balance.toFixed(2)}`);

            // Auto-play free spins
            if (gameState.inFreeSpins && gameState.freeSpinsRemaining > 0) {
                console.log(`🎁 Auto-playing free spin, ${gameState.freeSpinsRemaining} remaining`);
                setTimeout(() => {
                    if (gameState.inFreeSpins && gameState.freeSpinsRemaining > 0) {
                        spin();
                    }
                }, 1500);
            }

            // Check if free spins ended
            if (gameState.inFreeSpins && gameState.freeSpinsRemaining <= 0) {
                gameState.inFreeSpins = false;
                hideElement('freespins-display');
                showMessage(`🎊 Free Spins Complete! Total won: $${gameState.freeSpinsTotalWin.toFixed(2)}`);
                console.log('Free spins ended, total won:', gameState.freeSpinsTotalWin);
                gameState.freeSpinsTotalWin = 0;
            }
        }

        // 🎰 UPDATE SPIN BUTTON STATE
        function updateSpinButtonState() {
            const spinButton = document.getElementById('spin-button');
            const spinText = document.getElementById('spin-text');

            if (spinButton && spinText) {
                if (gameState.isSpinning) {
                    spinButton.disabled = true;
                    spinText.textContent = 'SPINNING...';
                    spinButton.style.opacity = '0.6';
                    spinButton.style.cursor = 'not-allowed';
                } else {
                    spinButton.disabled = false;
                    spinText.textContent = gameState.inFreeSpins ? `FREE SPIN (${gameState.freeSpinsRemaining})` : 'SPIN';
                    spinButton.style.opacity = '1';
                    spinButton.style.cursor = 'pointer';
                }
            }
        }

        // 🎉 PROFESSIONAL WIN CELEBRATION SYSTEM
        function showWinCelebration(winAmount, winType = 'WIN') {
            const multiplier = winAmount / gameState.currentBet;
            let message = '';
            let celebrationType = 'normal';

            // 🎭 LOSS DISGUISED AS WIN (LDW) - Celebrate even small wins
            if (winType === 'LDW') {
                message = `🎉 WIN! $${winAmount.toFixed(2)}`;
                celebrationType = 'ldw';
                console.log(`🎭 LDW Celebration: Won $${winAmount.toFixed(2)} on $${gameState.currentBet.toFixed(2)} bet (net loss)`);
            }
            // 🎊 BIG WIN CELEBRATIONS
            else if (multiplier >= 20) {
                message = `🎆 MEGA WIN! $${winAmount.toFixed(2)} (${multiplier.toFixed(1)}x)`;
                celebrationType = 'mega';
            } else if (multiplier >= 10) {
                message = `🎊 BIG WIN! $${winAmount.toFixed(2)} (${multiplier.toFixed(1)}x)`;
                celebrationType = 'big';
            } else if (multiplier >= 5) {
                message = `🎉 NICE WIN! $${winAmount.toFixed(2)} (${multiplier.toFixed(1)}x)`;
                celebrationType = 'nice';
            } else if (multiplier >= 1) {
                message = `💰 WIN! $${winAmount.toFixed(2)} (${multiplier.toFixed(1)}x)`;
                celebrationType = 'normal';
            } else {
                message = `💰 WIN! $${winAmount.toFixed(2)}`;
                celebrationType = 'small';
            }

            showMessage(message);

            // 🎨 VISUAL CELEBRATION EFFECTS
            const lastWinElement = document.getElementById('last-win-value');
            if (lastWinElement) {
                // Different animations based on win size
                let animationClass = 'flash';
                if (celebrationType === 'mega' || celebrationType === 'big') {
                    animationClass = 'flash';
                    lastWinElement.style.color = '#FFD700';
                    lastWinElement.style.textShadow = '0 0 20px #FFD700';
                } else if (celebrationType === 'ldw') {
                    lastWinElement.style.color = '#FF6B35';
                    lastWinElement.style.textShadow = '0 0 10px #FF6B35';
                }

                lastWinElement.style.animation = `${animationClass} 0.8s ease-in-out`;
                setTimeout(() => {
                    lastWinElement.style.animation = '';
                    lastWinElement.style.color = '#fff';
                    lastWinElement.style.textShadow = '0 0 10px rgba(255, 255, 255, 0.5)';
                }, 800);
            }

            // 🎵 ENHANCED AUDIO FEEDBACK
            if (celebrationType === 'mega' || celebrationType === 'big') {
                // Play special big win sound sequence
                setTimeout(() => audioSystem.playWinSound(winAmount, gameState.currentBet), 100);
                setTimeout(() => audioSystem.playWinSound(winAmount, gameState.currentBet), 300);
            }
        }

        async function animateSpinning() {
            console.log('🎰 Starting spin animation...');
            const spinDuration = 2000; // 2 seconds
            const changeInterval = 100; // Change symbols every 100ms
            const totalChanges = spinDuration / changeInterval;

            // Start spinning animation for all symbols
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    const element = document.getElementById(`symbol-${reel}-${row}`);
                    if (element) {
                        element.classList.add('spinning');
                        console.log(`Added spinning class to symbol-${reel}-${row}`);
                    } else {
                        console.error(`Element symbol-${reel}-${row} not found!`);
                    }
                }
            }

            // Play reel spinning sound
            if (audioSystem && audioSystem.playReelSpinSound) {
                setTimeout(() => {
                    audioSystem.playReelSpinSound();
                }, 200);
            }

            // Change symbols rapidly during spin
            for (let i = 0; i < totalChanges; i++) {
                for (let reel = 0; reel < config.reels; reel++) {
                    for (let row = 0; row < config.rows; row++) {
                        const randomSymbol = getRandomSymbol();
                        updateSymbolDisplay(reel, row, randomSymbol);
                    }
                }
                await new Promise(resolve => setTimeout(resolve, changeInterval));
            }

            // Remove spinning animation
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    const element = document.getElementById(`symbol-${reel}-${row}`);
                    if (element) {
                        element.classList.remove('spinning');
                    }
                }
            }

            // Stop reel spinning sound
            if (audioSystem && audioSystem.stopReelSpinSound) {
                audioSystem.stopReelSpinSound();
            }

            console.log('✅ Spin animation completed');
        }

        function generateNewGrid() {
            console.log('🎰 Generating smart grid with player analytics...');

            // If cooling spin, force no winning combinations
            if (gameState.isCoolingSpin) {
                console.log('🧊 Cooling spin - forcing no wins');
                generateNoWinGrid();
                return;
            }

            // 🎰 SPECIAL FREE SPINS LOGIC - Enhanced scatter frequency and guaranteed returns
            if (gameState.inFreeSpins) {
                generateFreeSpinsGrid();
                return;
            }

            // 🎯 USE SMART REEL SELECTION SYSTEM
            const selectedReels = reelEngine.selectOptimalReels();
            console.log(`🎰 Using reel set: ${playerAnalytics.currentReelSet}`);

            // Generate grid using weighted reel strips
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    const symbol = reelEngine.generateSymbol(reel, selectedReels);
                    symbolGrid[reel][row] = symbol;
                    updateSymbolDisplay(reel, row, symbol);
                }
            }

            // 🧠 PSYCHOLOGY ENGINE CHECKS
            const shouldForceWin = psychologyEngine.shouldForceWin();
            if (shouldForceWin) {
                console.log('🎯 FORCING WIN for player retention');
                forceSmallWin();
            }

            // Update analytics after grid generation
            psychologyEngine.updateAnalytics({
                totalWin: 0, // Will be updated in cascade
                scatterCount: countScatters(),
                wins: [],
                isLoss: false
            });
        }

        // 🎰 SPECIAL FREE SPINS GRID GENERATION
        function generateFreeSpinsGrid() {
            console.log('✨ Generating special free spins grid with enhanced returns...');

            // Calculate how much we still need to return to player
            const remainingTarget = gameState.freeSpinsTargetReturn - gameState.freeSpinsTotalWin;
            const spinsLeft = gameState.freeSpinsRemaining;
            const shouldGiveBigWin = remainingTarget > 0 && (spinsLeft <= 3 || Math.random() < 0.4);

            console.log(`💰 Free spins progress: Won $${gameState.freeSpinsTotalWin.toFixed(2)} / Target $${gameState.freeSpinsTargetReturn.toFixed(2)}, ${spinsLeft} spins left`);

            // Generate base grid with enhanced high-value symbols
            const freeSpinsSymbols = ['A', 'K', 'Q', 'J', '10', '🃏']; // More high-value symbols

            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    // 60% chance for high-value symbols during free spins
                    if (Math.random() < 0.6) {
                        symbolGrid[reel][row] = freeSpinsSymbols[Math.floor(Math.random() * freeSpinsSymbols.length)];
                    } else {
                        symbolGrid[reel][row] = getRandomSymbol();
                    }
                    updateSymbolDisplay(reel, row, symbolGrid[reel][row]);
                }
            }

            // 🌟 SPECIAL SCATTER PLACEMENT - More frequent during free spins
            if (Math.random() < 0.25) { // 25% chance for scatters in free spins
                const scatterCount = Math.random() < 0.7 ? 3 : (Math.random() < 0.9 ? 4 : 5);
                placeSpecialScatters(scatterCount);
                console.log(`✨ Placed ${scatterCount} special scatters for retrigger`);
            }

            // 💰 FORCE GOOD WINS if we need to meet target return
            if (shouldGiveBigWin) {
                forceFreeSpinsWin(remainingTarget, spinsLeft);
            }
        }

        // 🌟 PLACE SPECIAL SCATTERS DURING FREE SPINS
        function placeSpecialScatters(count) {
            const positions = [];

            // Generate random positions for scatters
            while (positions.length < count) {
                const reel = Math.floor(Math.random() * config.reels);
                const row = Math.floor(Math.random() * config.rows);
                const posKey = `${reel}-${row}`;

                if (!positions.some(pos => `${pos.reel}-${pos.row}` === posKey)) {
                    positions.push({reel, row});
                }
            }

            // Place scatters
            positions.forEach(pos => {
                symbolGrid[pos.reel][pos.row] = '⭐';
                updateSymbolDisplay(pos.reel, pos.row, '⭐');
            });
        }

        // 💰 FORCE GOOD WINS DURING FREE SPINS
        function forceFreeSpinsWin(targetAmount, spinsLeft) {
            const winPerSpin = targetAmount / Math.max(spinsLeft, 1);

            if (winPerSpin > gameState.currentBet * 2) {
                // Force a big line win with high-value symbols
                const bigWinSymbol = ['A', 'K'][Math.floor(Math.random() * 2)];
                const lineLength = Math.random() < 0.3 ? 5 : 4; // 30% chance for 5-symbol line

                // Place winning line in middle row
                for (let reel = 0; reel < lineLength; reel++) {
                    symbolGrid[reel][1] = bigWinSymbol;
                    updateSymbolDisplay(reel, 1, bigWinSymbol);
                }

                console.log(`💰 Forced big free spins win: ${lineLength}x ${bigWinSymbol} (targeting $${winPerSpin.toFixed(2)})`);
            } else {
                // Force smaller consistent wins
                const winSymbol = ['Q', 'J'][Math.floor(Math.random() * 2)];
                for (let reel = 0; reel < 3; reel++) {
                    symbolGrid[reel][1] = winSymbol;
                    updateSymbolDisplay(reel, 1, winSymbol);
                }
                console.log(`💰 Forced small free spins win: 3x ${winSymbol}`);
            }
        }

        // 💰 FORCE SMALL WIN SYSTEM
        function forceSmallWin() {
            // Force a small 3-symbol win in the middle row
            const winSymbol = ['J', 'Q', 'K'][Math.floor(Math.random() * 3)];

            // Place 3 matching symbols in middle row (positions 0, 1, 2)
            for (let reel = 0; reel < 3; reel++) {
                symbolGrid[reel][1] = winSymbol;
                updateSymbolDisplay(reel, 1, winSymbol);
            }

            console.log(`💰 Forced small win: 3x ${winSymbol} in middle row`);
        }

        // Count scatters on the grid
        function countScatters() {
            let count = 0;
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    if (symbolGrid[reel][row] === '⭐') {
                        count++;
                    }
                }
            }
            return count;
        }

        // Simplified functions for compatibility

        function generateNoWinGrid() {
            // Generate a grid with no winning combinations for cooling spin
            const symbols = ['9', '10', 'J', 'Q', 'K']; // Only low symbols, no wilds/scatters

            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    // Ensure no 3+ consecutive symbols
                    let symbol;
                    do {
                        symbol = symbols[Math.floor(Math.random() * symbols.length)];
                    } while (reel >= 2 &&
                             symbolGrid[reel-1] && symbolGrid[reel-2] &&
                             symbolGrid[reel-1][row] === symbol &&
                             symbolGrid[reel-2][row] === symbol);

                    symbolGrid[reel][row] = symbol;
                    updateSymbolDisplay(reel, row, symbol);
                }
            }
        }

        async function cascadeSequence() {
            // Check if this is a cooling spin (forced loss after 5x)
            if (gameState.isCoolingSpin) {
                console.log('Cooling spin - forcing no wins');
                gameState.isCoolingSpin = false;
                gameState.currentMultiplier = 1;
                gameState.consecutiveWins = 0;
                gameState.lastWin = 0;

                // Track losses for psychology
                playerAnalytics.consecutiveLosses++;

                updateDisplay();

                // Return spin result for analytics
                return {
                    totalWin: 0,
                    scatterCount: 0,
                    wins: [],
                    isLoss: true
                };
            }

            let cascadeCount = 0;
            let hasWins = true;
            let totalWinThisSpin = 0;

            while (hasWins) {
                // Fill empty positions
                await fillEmptyPositions();

                // Check for wins
                const wins = checkWins();
                console.log('Wins found:', wins);

                if (wins.length > 0) {
                    cascadeCount++;

                    // Progressive multiplier system (1x → 5x) - More frequent small multipliers
                    if (cascadeCount === 1) {
                        // First win in this spin
                        gameState.consecutiveWins++;
                        gameState.currentMultiplier = Math.min(gameState.consecutiveWins, config.maxMultiplier);

                        // Small win boost - give players more 1x-3x experiences with sound
                        if (gameState.currentMultiplier <= 3 && playerAnalytics.spinsWithoutWin > 5) {
                            console.log(`Small multiplier hit: ${gameState.currentMultiplier}x - player feels winning!`);
                        }

                        // Play multiplier sound for any multiplier increase
                        if (gameState.currentMultiplier > 1) {
                            audioSystem.playMultiplierSound();
                            console.log(`Multiplier increased to ${gameState.currentMultiplier}x - exciting build-up!`);
                        }

                        // Check if we need to set cooling spin for next round
                        if (gameState.currentMultiplier === 5) {
                            gameState.isCoolingSpin = true;
                            console.log('Reached 5x - next spin will be cooling spin');
                        }
                    }

                    // Track analytics
                    playerAnalytics.consecutiveLosses = 0;
                    playerAnalytics.spinsWithoutWin = 0;

                    // Calculate win amount
                    const winAmount = calculateWinAmount(wins);
                    gameState.lastWin += winAmount;
                    gameState.balance += winAmount;
                    totalWinThisSpin += winAmount;

                    // Track analytics wins
                    playerAnalytics.totalWon += winAmount;
                    playerAnalytics.netLoss = playerAnalytics.totalWagered - playerAnalytics.totalWon;

                    // Track user activity for wins
                    if (userSystem.isLoggedIn && winAmount > 0) {
                        userSystem.currentUser.balance = gameState.balance;
                        userSystem.trackGameActivity('win', winAmount);
                        userSystem.updateUserDisplay();
                    }

                    // 🎰 TRACK FREE SPINS WINNINGS for guaranteed return system
                    if (gameState.inFreeSpins) {
                        gameState.freeSpinsTotalWin += winAmount;
                        console.log(`💰 Free spins total: $${gameState.freeSpinsTotalWin.toFixed(2)} / Target: $${gameState.freeSpinsTargetReturn.toFixed(2)}`);
                    }

                    // Track big wins
                    const winMultiplier = winAmount / gameState.currentBet;
                    if (winMultiplier >= 10) {
                        playerAnalytics.lastBigWinSpin = playerAnalytics.totalSpins;
                        console.log(`Big win detected: ${winMultiplier.toFixed(1)}x bet`);
                    }

                    // Loss-Disguised-as-Win (LDW) tracking
                    if (winAmount < gameState.currentBet && winAmount >= gameState.currentBet * 0.1) {
                        playerAnalytics.ldwCount++;
                        console.log(`🎊 LDW CELEBRATION: Won $${winAmount.toFixed(2)} on $${gameState.currentBet.toFixed(2)} bet`);

                        setTimeout(() => {
                            audioSystem.playWinSound(winAmount, gameState.currentBet, 1);
                        }, 200);
                    }

                    console.log('Win amount:', winAmount, 'Multiplier:', gameState.currentMultiplier);

                    // Show multiplier if > 1
                    if (gameState.currentMultiplier > 1) {
                        showElement('multiplier-display');
                        document.getElementById('multiplier-value').textContent = `x${gameState.currentMultiplier}`;
                    }

                    // Highlight wins
                    await highlightWins(wins);

                    // Remove winning symbols
                    await removeWinningSymbols(wins);

                    // Check for bonus triggers
                    checkBonusTriggers(wins);

                    updateDisplay();
                } else {
                    hasWins = false;
                }
            }

            // Handle no wins - reset multiplier progression
            if (totalWinThisSpin === 0) {
                gameState.consecutiveWins = 0;
                gameState.currentMultiplier = 1;
                playerAnalytics.consecutiveLosses++;
                playerAnalytics.spinsWithoutWin++;
                console.log('No wins - reset multiplier to 1x');
            }

            // Show win celebration with enhanced audio
            if (gameState.lastWin > 0) {
                audioSystem.playModernWin(gameState.lastWin, gameState.currentBet);
                showWinCelebration(gameState.lastWin);

                // Extra cheer sound for big wins
                const winMultiplier = gameState.lastWin / gameState.currentBet;
                if (winMultiplier >= 15) {
                    setTimeout(() => {
                        audioSystem.playCheerSound();
                    }, 1000);
                }
            }

            // Hide multiplier if back to base
            if (gameState.currentMultiplier <= 1) {
                hideElement('multiplier-display');
            }

            // Count scatters for analytics
            let scatterCount = 0;
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    if (symbolGrid[reel][row] === '⭐') {
                        scatterCount++;
                    }
                }
            }

            // Return spin result for analytics
            return {
                totalWin: totalWinThisSpin,
                scatterCount: scatterCount,
                wins: [],
                isLoss: totalWinThisSpin === 0
            };
        }

        async function fillEmptyPositions() {
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = config.rows - 1; row >= 0; row--) {
                    if (!symbolGrid[reel][row]) {
                        const symbol = getRandomSymbol();
                        symbolGrid[reel][row] = symbol;

                        const element = document.getElementById(`symbol-${reel}-${row}`);
                        if (element) {
                            element.classList.add('dropping');
                            updateSymbolDisplay(reel, row, symbol);

                            setTimeout(() => {
                                element.classList.remove('dropping');
                            }, 800);
                        }
                    }
                }
            }

            await new Promise(resolve => setTimeout(resolve, 500));
        }

        function checkWins() {
            const wins = [];

            // Check ways-to-win (left to right)
            for (let row = 0; row < config.rows; row++) {
                let count = 1;
                let currentSymbol = symbolGrid[0][row];
                let positions = [{reel: 0, row: row}];

                if (!currentSymbol || currentSymbol === '⭐') continue;

                for (let reel = 1; reel < config.reels; reel++) {
                    const symbol = symbolGrid[reel][row];
                    if (symbol === currentSymbol || symbol === '🃏') {
                        count++;
                        positions.push({reel: reel, row: row});
                    } else {
                        break;
                    }
                }

                if (count >= 3) {
                    wins.push({
                        symbol: currentSymbol,
                        count: count,
                        positions: positions
                    });

                    // Play card match sounds for card symbols in line wins (like Super Ace)
                    if (['J', 'Q', 'K', 'A'].includes(currentSymbol)) {
                        if (count >= 5) {
                            // Special combo sound for 5 cards in a line
                            setTimeout(() => {
                                audioSystem.playCardComboSound(currentSymbol, count);
                            }, 400);
                        } else if (count >= 4) {
                            // Card combo sound for 4 cards
                            setTimeout(() => {
                                audioSystem.playCardComboSound(currentSymbol, count);
                            }, 350);
                        } else {
                            // Regular card match sound for 3 cards
                            setTimeout(() => {
                                audioSystem.playCardMatchSound(currentSymbol, count);
                            }, 300);
                        }
                    }
                }
            }

            // Check for scatter wins
            const scatterPositions = [];
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    if (symbolGrid[reel][row] === '⭐') {
                        scatterPositions.push({reel, row});
                    }
                }
            }

            if (scatterPositions.length >= 3) {
                wins.push({
                    symbol: '⭐',
                    count: scatterPositions.length,
                    positions: scatterPositions
                });

                // 🃏 Play scatter sound - rare and exciting
                setTimeout(() => {
                    audioSystem.playScatterSound();
                }, 100);
            }

            // Additional ChatGPT-style middle row count-based wins for frequent small wins
            const middleRowWins = checkMiddleRowCountWins();
            wins.push(...middleRowWins);

            return wins;
        }

        // ChatGPT-inspired middle row count-based win system for more frequent small wins
        function checkMiddleRowCountWins() {
            const wins = [];
            const middleRow = [];

            // Get middle row (row index 1 for 4-row grid)
            for (let reel = 0; reel < config.reels; reel++) {
                middleRow.push(symbolGrid[reel][1]);
            }

            // Count occurrences of each symbol
            const symbolCounts = {};
            middleRow.forEach(symbol => {
                if (symbol !== '⭐') { // Exclude scatters from count wins
                    symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
                }
            });

            // Check for 3+ same symbols (ChatGPT style) - gives more frequent small wins
            for (const [symbol, count] of Object.entries(symbolCounts)) {
                if (count >= 3) {
                    // Create positions for middle row
                    const positions = [];
                    for (let reel = 0; reel < config.reels; reel++) {
                        if (symbolGrid[reel][1] === symbol) {
                            positions.push({reel, row: 1});
                        }
                    }

                    wins.push({
                        symbol: symbol,
                        count: count,
                        positions: positions,
                        type: 'middle-row-count'
                    });

                    // Play card match sounds for card symbols (like Super Ace)
                    if (['J', 'Q', 'K', 'A'].includes(symbol)) {
                        if (count >= 4) {
                            // Special combo sound for 4+ cards
                            setTimeout(() => {
                                audioSystem.playCardComboSound(symbol, count);
                            }, 200);
                        } else {
                            // Regular card match sound for 3 cards
                            setTimeout(() => {
                                audioSystem.playCardMatchSound(symbol, count);
                            }, 200);
                        }
                    }

                    console.log(`ChatGPT-style count win: ${count}x ${symbol} in middle row - frequent small win!`);
                }
            }

            return wins;
        }

        function calculateWinAmount(wins) {
            let totalWin = 0;

            wins.forEach(win => {
                const payout = config.payouts[win.symbol];
                if (payout && payout[win.count]) {
                    totalWin += payout[win.count] * gameState.currentBet;
                }
            });

            return totalWin * gameState.currentMultiplier;
        }

        async function highlightWins(wins) {
            // Clear previous highlights
            document.querySelectorAll('.symbol').forEach(el => {
                el.classList.remove('winning');
            });

            // Highlight winning symbols
            wins.forEach(win => {
                win.positions.forEach(pos => {
                    const element = document.getElementById(`symbol-${pos.reel}-${pos.row}`);
                    if (element) {
                        element.classList.add('winning');
                    }
                });
            });

            await new Promise(resolve => setTimeout(resolve, 1500));
        }

        async function removeWinningSymbols(wins) {
            const positionsToRemove = new Set();

            wins.forEach(win => {
                win.positions.forEach(pos => {
                    positionsToRemove.add(`${pos.reel}-${pos.row}`);
                });
            });

            // Remove symbols
            positionsToRemove.forEach(posKey => {
                const [reel, row] = posKey.split('-').map(Number);
                symbolGrid[reel][row] = null;

                const element = document.getElementById(`symbol-${reel}-${row}`);
                if (element) {
                    element.textContent = '';
                    element.classList.remove('winning');
                }
            });

            await new Promise(resolve => setTimeout(resolve, 300));
        }

        function checkBonusTriggers(wins) {
            const scatterWin = wins.find(win => win.symbol === '⭐');
            if (scatterWin && scatterWin.count >= 3) {
                playerAnalytics.spinsWithoutScatter = 0; // Reset counter

                if (!gameState.inFreeSpins) {
                    triggerFreeSpins(scatterWin.count);
                } else {
                    retriggerFreeSpins();
                }
            }
        }

        function triggerFreeSpins(scatterCount) {
            // Always 10 free spins regardless of scatter count (casino style)
            const spinsAwarded = config.freeSpinsBase;
            gameState.inFreeSpins = true;
            gameState.freeSpinsRemaining = spinsAwarded;

            // 💰 CALCULATE GUARANTEED RETURN AMOUNT (20-30% of recent losses)
            const recentLosses = Math.max(playerAnalytics.netLoss, gameState.currentBet * 10);
            const returnPercentage = 0.20 + (Math.random() * 0.10); // 20-30%
            gameState.freeSpinsTargetReturn = recentLosses * returnPercentage;
            gameState.freeSpinsTotalWin = 0;

            console.log(`🎰 FREE SPINS: Target return $${gameState.freeSpinsTargetReturn.toFixed(2)} (${(returnPercentage*100).toFixed(1)}% of $${recentLosses.toFixed(2)} losses)`);

            showElement('freespins-display');
            document.getElementById('freespins-value').textContent = spinsAwarded;

            // Play bonus sound for free spins
            audioSystem.playBonusSound();

            showMessage(`🎊 ${spinsAwarded} FREE SPINS AWARDED! 🎊`);

            // Show encouraging message about the special free spins
            setTimeout(() => {
                showMessage('✨ Special scatters active during free spins! ✨', 'info');
            }, 2000);

            console.log('Free spins triggered - Player balance:', gameState.balance);
        }

        // Simple free spins - no auto-play for now
        function autoPlayFreeSpins() {
            // Removed auto-play functionality for simplicity
            console.log('Free spins active - manual play');
        }

        function retriggerFreeSpins() {
            // Only +5 spins and max 1 retrigger
            gameState.freeSpinsRemaining += config.freeSpinsRetrigger;
            document.getElementById('freespins-value').textContent = gameState.freeSpinsRemaining;
            showMessage(`+${config.freeSpinsRetrigger} FREE SPINS!`);
        }

        // Betting Functions
        function decreaseBet() {
            if (gameState.isSpinning || gameState.inFreeSpins) return;

            if (currentBetIndex > 0) {
                currentBetIndex--;
                gameState.currentBet = config.betLevels[currentBetIndex];
                updateDisplay();
            }
        }

        function increaseBet() {
            if (gameState.isSpinning || gameState.inFreeSpins) return;

            if (currentBetIndex < config.betLevels.length - 1 && gameState.balance >= config.betLevels[currentBetIndex + 1]) {
                currentBetIndex++;
                gameState.currentBet = config.betLevels[currentBetIndex];
                updateDisplay();
            }
        }

        function setMaxBet() {
            if (gameState.isSpinning || gameState.inFreeSpins) return;

            // Find highest affordable bet
            let maxIndex = 0;
            for (let i = config.betLevels.length - 1; i >= 0; i--) {
                if (gameState.balance >= config.betLevels[i]) {
                    maxIndex = i;
                    break;
                }
            }

            currentBetIndex = maxIndex;
            gameState.currentBet = config.betLevels[currentBetIndex];
            updateDisplay();
            console.log('Max bet set to:', gameState.currentBet);
        }

        // 🎵 Howler.js Music Toggle Functionality - Complete Audio Control
        function toggleBackgroundMusic() {
            const musicButton = document.getElementById('music-toggle');

            // Use the audioSystem's built-in toggle method
            const isEnabled = audioSystem.toggleMusic();

            if (isEnabled) {
                musicButton.textContent = '🎼 MUSIC ON';
                console.log('🎵 HOWLER.JS MUSIC ENABLED - All casino sounds active!');
            } else {
                musicButton.textContent = '🔇 MUSIC OFF';
                console.log('🔇 HOWLER.JS MUSIC DISABLED - Silent mode');
            }
        }

        // UI Functions
        function updateDisplay() {
            // Sync user balance with backend database
            if (currentUser) {
                // Update balance in backend when it changes
                fetch('users-api.php?action=update_balance', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: currentUser.id,
                        balance: gameState.balance
                    })
                }).catch(error => {
                    console.error('Error updating balance:', error);
                });
            }

            document.getElementById('balance').textContent = `$${gameState.balance.toFixed(2)}`;
            document.getElementById('current-bet').textContent = `$${gameState.currentBet.toFixed(2)}`;
            document.getElementById('bet-display').textContent = `$${gameState.currentBet.toFixed(2)}`;
            document.getElementById('last-win').textContent = `$${gameState.lastWin.toFixed(2)}`;

            // Update header multiplier display
            document.getElementById('header-multiplier-value').textContent = `${gameState.currentMultiplier}X`;

            // Update free spins display if active
            if (gameState.inFreeSpins) {
                document.getElementById('freespins-value').textContent = gameState.freeSpinsRemaining;
            }

            // 🎰 CENTRALIZED SPIN BUTTON STATE MANAGEMENT
            updateSpinButtonState();
        }

        // 🎰 DEDICATED SPIN BUTTON STATE FUNCTION - FIXES ALL BUTTON ISSUES
        function updateSpinButtonState() {
            const spinButton = document.getElementById('spin-button');
            const spinText = document.getElementById('spin-text');

            if (!spinButton || !spinText) return;

            if (gameState.isSpinning) {
                spinButton.disabled = true;
                spinText.textContent = 'SPINNING...';
                spinButton.style.opacity = '0.6';
                spinButton.style.cursor = 'not-allowed';
                spinButton.style.pointerEvents = 'none';
            } else if (gameState.inFreeSpins) {
                spinButton.disabled = false;
                spinText.textContent = `FREE SPIN (${gameState.freeSpinsRemaining})`;
                spinButton.style.opacity = '1';
                spinButton.style.cursor = 'pointer';
                spinButton.style.pointerEvents = 'auto';
            } else {
                const canSpin = gameState.balance >= gameState.currentBet;
                spinButton.disabled = !canSpin;
                spinText.textContent = 'SPIN';
                spinButton.style.opacity = canSpin ? '1' : '0.6';
                spinButton.style.cursor = canSpin ? 'pointer' : 'not-allowed';
                spinButton.style.pointerEvents = canSpin ? 'auto' : 'none';
            }
        }

        function showWinCelebration(amount) {
            const winMultiplier = amount / gameState.currentBet;
            let winType = 'WIN!';

            if (winMultiplier >= 50) {
                winType = 'MEGA WIN!';
            } else if (winMultiplier >= 25) {
                winType = 'SUPER WIN!';
            } else if (winMultiplier >= 10) {
                winType = 'BIG WIN!';
            }

            document.getElementById('win-amount-display').textContent = `$${amount.toFixed(2)}`;
            document.getElementById('win-type-display').textContent = winType;
            showElement('win-popup');

            setTimeout(() => {
                hideElement('win-popup');
            }, 3000);
        }

        function showMessage(text) {
            const messageEl = document.getElementById('message-display');
            messageEl.textContent = text;
            showElement('message-display');

            setTimeout(() => {
                hideElement('message-display');
            }, 3000);
        }

        function showElement(id) {
            document.getElementById(id).classList.remove('hidden');
        }

        function hideElement(id) {
            document.getElementById(id).classList.add('hidden');
        }

        // 🎰 BET CONTROL FUNCTIONS
        function decreaseBet() {
            if (currentBetIndex > 0) {
                currentBetIndex--;
                gameState.currentBet = config.betLevels[currentBetIndex];
                updateDisplay();
                console.log(`Bet decreased to $${gameState.currentBet.toFixed(2)}`);
            }
        }

        function increaseBet() {
            if (currentBetIndex < config.betLevels.length - 1) {
                currentBetIndex++;
                gameState.currentBet = config.betLevels[currentBetIndex];
                updateDisplay();
                console.log(`Bet increased to $${gameState.currentBet.toFixed(2)}`);
            }
        }

        function setMaxBet() {
            currentBetIndex = config.betLevels.length - 1;
            gameState.currentBet = config.betLevels[currentBetIndex];
            updateDisplay();
            console.log(`Max bet set to $${gameState.currentBet.toFixed(2)}`);
        }

        // 🎵 MUSIC CONTROL
        function toggleBackgroundMusic() {
            const musicEnabled = audioSystem.toggleMusic();
            const musicButton = document.getElementById('music-toggle');
            if (musicButton) {
                musicButton.textContent = musicEnabled ? '🎼 MUSIC ON' : '🔇 MUSIC OFF';
            }
        }

        // 🎰 MISSING FUNCTIONS FOR COMPATIBILITY
        function cascadeSequence() {
            // Simple cascade - just check for wins and return result
            const wins = checkWins();
            const totalWin = wins.reduce((sum, win) => sum + win.amount, 0);

            if (totalWin > 0) {
                gameState.lastWin = totalWin;
                gameState.balance += totalWin;

                // Play win sound and show celebration
                audioSystem.playWinSound(totalWin, gameState.currentBet);
                showWinCelebration(totalWin);

                console.log(`💰 Win: $${totalWin.toFixed(2)}`);
            }

            return {
                totalWin: totalWin,
                scatterCount: countScatters(),
                wins: wins,
                isLoss: totalWin === 0
            };
        }

        function checkWins() {
            const wins = [];

            // Check horizontal lines for wins
            for (let row = 0; row < config.rows; row++) {
                let consecutiveCount = 1;
                let currentSymbol = symbolGrid[0][row];

                if (!currentSymbol || currentSymbol === '⭐') continue; // Skip scatters

                for (let reel = 1; reel < config.reels; reel++) {
                    if (symbolGrid[reel][row] === currentSymbol || symbolGrid[reel][row] === '🃏') {
                        consecutiveCount++;
                    } else {
                        break;
                    }
                }

                // Check if we have a winning combination (3+ symbols)
                if (consecutiveCount >= 3) {
                    const payout = config.payouts[currentSymbol];
                    if (payout && payout[consecutiveCount - 1] > 0) {
                        const winAmount = payout[consecutiveCount - 1] * gameState.currentBet;
                        wins.push({
                            symbol: currentSymbol,
                            count: consecutiveCount,
                            amount: winAmount,
                            row: row,
                            positions: []
                        });
                    }
                }
            }

            return wins;
        }

        function countScatters() {
            let count = 0;
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    if (symbolGrid[reel][row] === '⭐') {
                        count++;
                    }
                }
            }
            return count;
        }

        // 🎰 SIMPLIFIED GRID GENERATION FUNCTIONS
        function generateFreeSpinsGrid() {
            console.log('✨ Generating free spins grid...');
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    const symbol = getRandomSymbol();
                    symbolGrid[reel][row] = symbol;
                    updateSymbolDisplay(reel, row, symbol);
                }
            }
        }

        function generateNoWinGrid() {
            console.log('🧊 Generating no-win grid...');
            const symbols = ['9', '10', 'J', 'Q', 'K']; // Only low symbols
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    const symbol = symbols[Math.floor(Math.random() * symbols.length)];
                    symbolGrid[reel][row] = symbol;
                    updateSymbolDisplay(reel, row, symbol);
                }
            }
        }

        function forceSmallWin() {
            console.log('💰 Forcing small win...');
            const winSymbol = ['J', 'Q', 'K'][Math.floor(Math.random() * 3)];
            // Place 3 matching symbols in middle row
            for (let reel = 0; reel < 3; reel++) {
                symbolGrid[reel][1] = winSymbol;
                updateSymbolDisplay(reel, 1, winSymbol);
            }
        }

        function forceFreeSpinsWin(targetAmount, spinsLeft) {
            console.log(`💰 Forcing free spins win: $${targetAmount.toFixed(2)}`);
            forceSmallWin(); // Simple implementation
        }

        function placeSpecialScatters(count) {
            console.log(`✨ Placing ${count} special scatters...`);
            let placed = 0;
            while (placed < count && placed < 5) {
                const reel = Math.floor(Math.random() * config.reels);
                const row = Math.floor(Math.random() * config.rows);
                if (symbolGrid[reel][row] !== '⭐') {
                    symbolGrid[reel][row] = '⭐';
                    updateSymbolDisplay(reel, row, '⭐');
                    placed++;
                }
            }
        }

        // 🎯 PROFESSIONAL WEIGHTED GRID GENERATION
        function generateWeightedGrid() {
            console.log('🎯 Generating weighted symbol grid...');

            // Get optimal reel set based on player analytics
            const selectedReels = reelEngine.selectOptimalReels();
            console.log(`🎰 Using reel set: ${playerAnalytics.currentReelSet}`);

            // Generate symbols using weighted distribution
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    const symbol = reelEngine.generateSymbol(reel, selectedReels);
                    symbolGrid[reel][row] = symbol;
                    updateSymbolDisplay(reel, row, symbol);
                }
            }

            // 🧠 APPLY PSYCHOLOGICAL ADJUSTMENTS
            const shouldForceWin = psychologyEngine.shouldForceWin();
            if (shouldForceWin) {
                console.log('🎯 FORCING WIN for player retention');
                forceSmallWinInGrid();
            }

            const shouldCreateNearMiss = psychologyEngine.shouldCreateNearMiss();
            if (shouldCreateNearMiss && !shouldForceWin) {
                console.log('🎭 Creating NEAR MISS for psychological engagement');
                createNearMissPattern();
            }
        }

        // 🎯 FORCE SMALL WIN IN EXISTING GRID
        function forceSmallWinInGrid() {
            const winSymbols = ['J', 'Q', 'K']; // Medium value symbols
            const winSymbol = winSymbols[Math.floor(Math.random() * winSymbols.length)];
            const winRow = Math.floor(Math.random() * config.rows);

            // Place 3 matching symbols in a random row
            for (let reel = 0; reel < 3; reel++) {
                symbolGrid[reel][winRow] = winSymbol;
                updateSymbolDisplay(reel, winRow, winSymbol);
            }

            console.log(`💰 Forced small win: 3x ${winSymbol} on row ${winRow + 1}`);
        }

        // 🎭 CREATE NEAR MISS PATTERN
        function createNearMissPattern() {
            const premiumSymbols = ['K', 'A'];
            const nearMissSymbol = premiumSymbols[Math.floor(Math.random() * premiumSymbols.length)];
            const nearMissRow = Math.floor(Math.random() * config.rows);

            // Place 2 matching premium symbols and 1 different
            symbolGrid[0][nearMissRow] = nearMissSymbol;
            symbolGrid[1][nearMissRow] = nearMissSymbol;
            symbolGrid[2][nearMissRow] = config.symbols[Math.floor(Math.random() * 5)]; // Different symbol

            updateSymbolDisplay(0, nearMissRow, symbolGrid[0][nearMissRow]);
            updateSymbolDisplay(1, nearMissRow, symbolGrid[1][nearMissRow]);
            updateSymbolDisplay(2, nearMissRow, symbolGrid[2][nearMissRow]);

            playerAnalytics.nearMissCount++;
            console.log(`🎭 Near miss created: 2x ${nearMissSymbol} on row ${nearMissRow + 1}`);
        }

        // 🎯 PROFESSIONAL WIN CHECKING SYSTEM
        function checkForWinsAdvanced() {
            const wins = [];
            let totalWin = 0;
            let scatterCount = 0;
            let wildCount = 0;

            // Count scatters and wilds
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    if (symbolGrid[reel][row] === '⭐') {
                        scatterCount++;
                    } else if (symbolGrid[reel][row] === '🃏') {
                        wildCount++;
                    }
                }
            }

            // Check horizontal paylines for wins
            for (let row = 0; row < config.rows; row++) {
                const lineResult = checkPayline(row);
                if (lineResult.win) {
                    wins.push(lineResult);
                    totalWin += lineResult.amount;
                }
            }

            // 🎁 FREE SPINS ENHANCEMENT - Higher payouts during bonus
            if (gameState.inFreeSpins) {
                totalWin *= 1.5; // 50% bonus on all wins during free spins
                console.log(`🎁 Free spins bonus: +50% on all wins`);
            }

            // 🎰 WILD MULTIPLIER SYSTEM
            if (wildCount > 0 && totalWin > 0) {
                const wildMultiplier = 1 + (wildCount * 0.5); // Each wild adds 50% multiplier
                totalWin *= wildMultiplier;
                console.log(`🃏 Wild multiplier: x${wildMultiplier.toFixed(1)} (${wildCount} wilds)`);
            }

            return {
                totalWin: totalWin,
                scatterCount: scatterCount,
                wildCount: wildCount,
                wins: wins,
                isLoss: totalWin === 0
            };
        }

        // 🎯 CHECK INDIVIDUAL PAYLINE
        function checkPayline(row) {
            let consecutiveCount = 1;
            let currentSymbol = symbolGrid[0][row];
            let winAmount = 0;

            // Skip scatters (they don't form paylines)
            if (!currentSymbol || currentSymbol === '⭐') {
                return { win: false, amount: 0, symbol: null, count: 0, row: row };
            }

            // Count consecutive symbols (including wilds)
            for (let reel = 1; reel < config.reels; reel++) {
                const reelSymbol = symbolGrid[reel][row];

                if (reelSymbol === currentSymbol || reelSymbol === '🃏') {
                    consecutiveCount++;
                } else if (currentSymbol === '🃏' && reelSymbol !== '⭐') {
                    // Wild can substitute for any symbol except scatter
                    currentSymbol = reelSymbol;
                    consecutiveCount++;
                } else {
                    break;
                }
            }

            // Check if we have a winning combination (3+ symbols)
            if (consecutiveCount >= 3) {
                const paySymbol = currentSymbol === '🃏' ? 'A' : currentSymbol; // Wilds pay as premium symbol
                const payout = config.payouts[paySymbol];

                if (payout && payout[consecutiveCount - 1] > 0) {
                    winAmount = payout[consecutiveCount - 1] * gameState.currentBet;
                    console.log(`💰 Payline ${row + 1}: ${consecutiveCount}x ${paySymbol} = $${winAmount.toFixed(2)}`);

                    return {
                        win: true,
                        amount: winAmount,
                        symbol: paySymbol,
                        count: consecutiveCount,
                        row: row
                    };
                }
            }

            return { win: false, amount: 0, symbol: null, count: 0, row: row };
        }

        // 🎁 PROFESSIONAL FREE SPINS SYSTEM - Variable Awards Based on Scatter Count
        function triggerFreeSpins(scatterCount) {
            console.log(`🎁 FREE SPINS TRIGGERED! ${scatterCount} scatters found`);

            // 🎰 PROPER SCATTER-BASED FREE SPINS AWARDS
            let freeSpinsAwarded = 0;
            let retriggerMessage = '';

            if (scatterCount === 3) {
                freeSpinsAwarded = 10;
                retriggerMessage = '🎊 3 SCATTERS = 10 FREE SPINS!';
            } else if (scatterCount === 4) {
                freeSpinsAwarded = 15;
                retriggerMessage = '🎊 4 SCATTERS = 15 FREE SPINS!';
            } else if (scatterCount >= 5) {
                freeSpinsAwarded = 25;
                retriggerMessage = '🎊 5 SCATTERS = 25 FREE SPINS!';
            }

            // 🔄 RETRIGGER LOGIC - Add to existing free spins if already in bonus
            if (gameState.inFreeSpins) {
                gameState.freeSpinsRemaining += freeSpinsAwarded;
                showMessage(`🔄 RETRIGGER! +${freeSpinsAwarded} FREE SPINS! Total: ${gameState.freeSpinsRemaining}`);
                console.log(`🔄 Free spins retriggered: +${freeSpinsAwarded}, Total: ${gameState.freeSpinsRemaining}`);
            } else {
                // 🆕 NEW FREE SPINS ROUND
                gameState.inFreeSpins = true;
                gameState.freeSpinsRemaining = freeSpinsAwarded;
                gameState.freeSpinsTotalWin = 0;

                // Calculate guaranteed return (20-40% of recent losses)
                const recentLosses = Math.max(playerAnalytics.netLoss, gameState.currentBet * 10);
                const returnPercentage = 0.20 + (Math.random() * 0.20); // 20-40%
                gameState.freeSpinsTargetReturn = recentLosses * returnPercentage;

                showMessage(retriggerMessage);
                console.log(`🎁 New free spins round: ${freeSpinsAwarded} spins, Target return: $${gameState.freeSpinsTargetReturn.toFixed(2)}`);

                // Update analytics
                playerAnalytics.freeSpinsTriggered++;
                playerAnalytics.lastBonusSpin = playerAnalytics.totalSpins;
                playerAnalytics.spinsWithoutScatter = 0;
            }

            // Update display
            showElement('freespins-display');
            document.getElementById('freespins-value').textContent = gameState.freeSpinsRemaining;

            // Auto-start first free spin after 2 seconds
            setTimeout(() => {
                if (gameState.inFreeSpins && gameState.freeSpinsRemaining > 0) {
                    spin();
                }
            }, 2000);
        }

        // 🎮 GAME INITIALIZATION FUNCTION
        function initGame() {
            console.log('🎮 Initializing Vegas Ace Slots...');

            // Initialize player analytics
            playerAnalytics.sessionStartTime = Date.now();
            playerAnalytics.sessionStartBalance = gameState.balance;

            // Set initial player classification
            playerClassifier.applyRTPProfile('newPlayer');

            // Create the reel grid
            createReels();

            // Populate with initial symbols
            populateReels();

            // Set up game controls
            setupGameControls();

            // Update display
            updateDisplay();
            updateAnalyticsDisplay();

            console.log('✅ Game initialized successfully');
        }

        // 🎮 SETUP GAME CONTROLS
        function setupGameControls() {
            console.log('🎮 Setting up game controls...');

            // Spin button
            const spinButton = document.getElementById('spin-button');
            if (spinButton) {
                spinButton.addEventListener('click', () => {
                    console.log('🎰 Spin button clicked');
                    spin();
                });
            }

            // Bet controls
            const betUpButton = document.getElementById('bet-up');
            const betDownButton = document.getElementById('bet-down');
            const maxBetButton = document.getElementById('max-bet');

            if (betUpButton) {
                betUpButton.addEventListener('click', () => {
                    increaseBet();
                });
            }

            if (betDownButton) {
                betDownButton.addEventListener('click', () => {
                    decreaseBet();
                });
            }

            if (maxBetButton) {
                maxBetButton.addEventListener('click', () => {
                    setMaxBet();
                });
            }

            // Music toggle
            const musicButton = document.getElementById('music-toggle');
            if (musicButton) {
                musicButton.addEventListener('click', () => {
                    toggleMusic();
                });
            }

            console.log('✅ Game controls set up');
        }

        // 🎰 CREATE REEL GRID
        function createReels() {
            console.log('🎰 Creating reel grid...');
            const reelsContainer = document.getElementById('reels');
            if (!reelsContainer) {
                console.error('❌ Reels container not found!');
                return;
            }

            reelsContainer.innerHTML = '';
            symbolGrid = [];

            for (let reel = 0; reel < config.reels; reel++) {
                symbolGrid[reel] = [];
                for (let row = 0; row < config.rows; row++) {
                    const symbolElement = document.createElement('div');
                    symbolElement.className = 'symbol';
                    symbolElement.id = `symbol-${reel}-${row}`;
                    reelsContainer.appendChild(symbolElement);
                    symbolGrid[reel][row] = null;
                }
            }

            console.log(`✅ Created ${config.reels}x${config.rows} reel grid`);
        }

        // 🎰 POPULATE REELS WITH INITIAL SYMBOLS
        function populateReels() {
            console.log('🎰 Populating reels with initial symbols...');
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    const symbol = getRandomSymbol();
                    symbolGrid[reel][row] = symbol;
                    updateSymbolDisplay(reel, row, symbol);
                }
            }
            console.log('✅ Reels populated');
        }

        // 💰 BET CONTROL FUNCTIONS
        function increaseBet() {
            if (currentBetIndex < config.betLevels.length - 1) {
                currentBetIndex++;
                gameState.currentBet = config.betLevels[currentBetIndex];
                updateDisplay();
                console.log(`💰 Bet increased to $${gameState.currentBet.toFixed(2)}`);
            }
        }

        function decreaseBet() {
            if (currentBetIndex > 0) {
                currentBetIndex--;
                gameState.currentBet = config.betLevels[currentBetIndex];
                updateDisplay();
                console.log(`💰 Bet decreased to $${gameState.currentBet.toFixed(2)}`);
            }
        }

        function setMaxBet() {
            currentBetIndex = config.betLevels.length - 1;
            gameState.currentBet = config.betLevels[currentBetIndex];
            updateDisplay();
            console.log(`💰 Max bet set to $${gameState.currentBet.toFixed(2)}`);
        }

        // 🎵 MUSIC CONTROL
        function toggleMusic() {
            const musicButton = document.getElementById('music-toggle');
            const bgMusic = document.getElementById('bgMusic');

            if (bgMusic.paused) {
                bgMusic.play();
                musicButton.textContent = '🎼 MUSIC ON';
            } else {
                bgMusic.pause();
                musicButton.textContent = '🎼 MUSIC OFF';
            }
        }

        // 📊 UPDATE DISPLAY FUNCTION
        function updateDisplay() {
            // Update balance
            const balanceElement = document.getElementById('balance');
            if (balanceElement) {
                balanceElement.textContent = `$${gameState.balance.toFixed(2)}`;
            }

            // Update current bet
            const currentBetElement = document.getElementById('current-bet');
            const betDisplayElement = document.getElementById('bet-display');
            if (currentBetElement) {
                currentBetElement.textContent = `$${gameState.currentBet.toFixed(2)}`;
            }
            if (betDisplayElement) {
                betDisplayElement.textContent = `$${gameState.currentBet.toFixed(2)}`;
            }

            // Update last win
            const lastWinElement = document.getElementById('last-win');
            if (lastWinElement) {
                lastWinElement.textContent = `$${gameState.lastWin.toFixed(2)}`;
            }

            // Update multiplier
            const multiplierElement = document.getElementById('header-multiplier-value');
            if (multiplierElement) {
                multiplierElement.textContent = `${gameState.currentMultiplier}X`;
            }

            // Update free spins display
            if (gameState.inFreeSpins) {
                showElement('freespins-display');
                const freeSpinsElement = document.getElementById('freespins-value');
                if (freeSpinsElement) {
                    freeSpinsElement.textContent = gameState.freeSpinsRemaining;
                }
            } else {
                hideElement('freespins-display');
            }
        }

        // 🎨 UTILITY FUNCTIONS
        function showElement(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.classList.remove('hidden');
            }
        }

        function hideElement(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.classList.add('hidden');
            }
        }

        function showMessage(message, type = 'info') {
            const messageElement = document.getElementById('message-display');
            if (messageElement) {
                messageElement.textContent = message;
                messageElement.classList.remove('hidden');

                setTimeout(() => {
                    messageElement.classList.add('hidden');
                }, 3000);
            }
            console.log(`📢 ${message}`);
        }

        // 🎰 SYMBOL DISPLAY UPDATE
        function updateSymbolDisplay(reel, row, symbol) {
            const element = document.getElementById(`symbol-${reel}-${row}`);
            if (element) {
                element.textContent = symbol;
                element.style.color = config.colors[symbol] || '#FFD700';
            }
        }

        // 📊 UPDATE ANALYTICS DISPLAY
        function updateAnalyticsDisplay() {
            // Update analytics display elements if they exist
            const elements = {
                'analytics-spins': playerAnalytics.totalSpins,
                'analytics-rtp': `${(playerAnalytics.currentSessionRTP * 100).toFixed(1)}%`,
                'analytics-type': playerAnalytics.playerType,
                'analytics-reels': playerAnalytics.currentReelSet,
                'analytics-losses': playerAnalytics.consecutiveLosses,
                'analytics-nowins': playerAnalytics.spinsWithoutWin,
                'analytics-nobonus': playerAnalytics.spinsWithoutScatter,
                'analytics-cooldown': Math.max(0, config.psychology.scatterCooldown - (playerAnalytics.totalSpins - playerAnalytics.lastBonusSpin))
            };

            for (const [id, value] of Object.entries(elements)) {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            }
        }

        // Game will be initialized after successful login
        // No automatic initialization on page load
    </script>
</body>
</html>

