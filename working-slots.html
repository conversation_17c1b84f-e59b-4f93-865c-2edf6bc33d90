<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vegas Ace Slots - Working Version</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', monospace;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a0a0a 25%, #2a1a0a 50%, #1a0a1a 75%, #0a0a2a 100%);
            color: #fff;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            animation: subtle-pulse 4s ease-in-out infinite alternate;
        }

        @keyframes subtle-pulse {
            0% { background-size: 100% 100%; }
            100% { background-size: 105% 105%; }
        }

        .game-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .header {
            text-align: center;
            padding: 25px;
            background: linear-gradient(145deg, rgba(255,215,0,0.1), rgba(255,69,0,0.1));
            border-radius: 20px;
            margin-bottom: 25px;
            border: 3px solid #ffd700;
            box-shadow: 0 0 30px rgba(255, 215, 0, 0.3), inset 0 0 20px rgba(255, 69, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,215,0,0.1), transparent);
            animation: shine 3s linear infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .title {
            font-size: 3rem;
            color: #ffd700;
            margin-bottom: 20px;
            text-shadow: 0 0 30px rgba(255, 215, 0, 0.8), 0 0 60px rgba(255, 69, 0, 0.4);
            font-weight: 900;
            letter-spacing: 3px;
            position: relative;
            z-index: 1;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 40px;
            flex-wrap: wrap;
        }

        .stat {
            text-align: center;
            background: linear-gradient(145deg, rgba(0,0,0,0.8), rgba(20,20,20,0.9));
            padding: 15px 25px;
            border-radius: 15px;
            border: 2px solid #ffd700;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.2), inset 0 0 10px rgba(255, 215, 0, 0.1);
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
        }

        .stat:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 25px rgba(255, 215, 0, 0.4);
        }

        .stat-label {
            font-size: 1rem;
            color: #ffd700;
            margin-bottom: 8px;
            font-weight: bold;
            letter-spacing: 1px;
        }

        .stat-value {
            font-size: 1.6rem;
            color: #fff;
            font-weight: 900;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .multiplier-stat {
            background: linear-gradient(145deg, rgba(255,69,0,0.8), rgba(255,140,0,0.9));
            border-color: #ff6b35;
            box-shadow: 0 0 25px rgba(255, 69, 0, 0.4), inset 0 0 15px rgba(255, 140, 0, 0.2);
        }

        .multiplier-value {
            color: #fff;
            font-size: 1.8rem;
            text-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
            animation: multiplier-glow 1.5s ease-in-out infinite alternate;
        }

        @keyframes multiplier-glow {
            0% {
                text-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
                transform: scale(1);
            }
            100% {
                text-shadow: 0 0 25px rgba(255, 255, 255, 1), 0 0 35px rgba(255, 69, 0, 0.8);
                transform: scale(1.05);
            }
        }

        .game-area {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
        }

        .slot-machine {
            background: linear-gradient(145deg, rgba(0,0,0,0.9), rgba(30,30,30,0.9));
            border: 4px solid #ffd700;
            border-radius: 25px;
            padding: 40px;
            box-shadow:
                0 0 60px rgba(255, 215, 0, 0.4),
                inset 0 0 30px rgba(255, 69, 0, 0.1),
                0 10px 30px rgba(0, 0, 0, 0.5);
            position: relative;
        }

        .slot-machine::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #ffd700, #ff6b35, #ffd700, #ff6b35);
            border-radius: 25px;
            z-index: -1;
            animation: border-glow 2s linear infinite;
        }

        @keyframes border-glow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .reels {
            display: grid;
            grid-template-columns: repeat(5, 100px);
            grid-template-rows: repeat(4, 100px);
            gap: 10px;
            margin-bottom: 20px;
        }

        .symbol {
            width: 100px;
            height: 100px;
            background: linear-gradient(145deg, #1a1a1a, #0a0a0a);
            border: 3px solid #ffd700;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.2rem;
            font-weight: 900;
            color: #ffd700;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 0 15px rgba(255, 215, 0, 0.3),
                inset 0 0 10px rgba(255, 215, 0, 0.1);
        }

        .symbol::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .symbol:hover::before {
            left: 100%;
        }

        .symbol.spinning {
            animation: spin-blur 0.2s ease-in-out infinite;
            border-color: #ff6b35;
            box-shadow: 0 0 15px rgba(255, 107, 53, 0.6);
        }

        .symbol.winning {
            background: linear-gradient(145deg, #ff6b35, #ff4500);
            box-shadow: 0 0 30px rgba(255, 215, 0, 1);
            animation: win-glow 1s ease-in-out infinite alternate;
            transform: scale(1.1);
        }

        .symbol.dropping {
            animation: drop-in 0.8s ease-out;
        }

        .controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            padding: 20px;
            background: rgba(0,0,0,0.4);
            border-radius: 15px;
            border: 2px solid #ffd700;
            flex-wrap: wrap;
        }

        .bet-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .btn {
            padding: 12px 20px;
            background: linear-gradient(145deg, #2a2a3e, #1a1a2e);
            border: 2px solid #ffd700;
            color: #ffd700;
            font-family: 'Orbitron', monospace;
            font-weight: bold;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .btn:hover:not(:disabled) {
            background: linear-gradient(145deg, #3a3a4e, #2a2a3e);
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.4);
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .spin-btn {
            padding: 20px 40px;
            background: linear-gradient(145deg, #ff6b35, #ff4500, #ff6b35);
            background-size: 200% 200%;
            color: white;
            font-size: 1.5rem;
            font-weight: 900;
            border: 4px solid #ffd700;
            min-width: 180px;
            border-radius: 15px;
            text-shadow: 0 0 10px rgba(0,0,0,0.8);
            box-shadow:
                0 0 30px rgba(255, 107, 53, 0.6),
                inset 0 0 20px rgba(255, 255, 255, 0.1);
            animation: pulse-glow 2s ease-in-out infinite alternate;
            position: relative;
            overflow: hidden;
        }

        .spin-btn::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: spin-shine 3s linear infinite;
        }

        @keyframes pulse-glow {
            0% {
                box-shadow: 0 0 30px rgba(255, 107, 53, 0.6), inset 0 0 20px rgba(255, 255, 255, 0.1);
                background-position: 0% 50%;
            }
            100% {
                box-shadow: 0 0 50px rgba(255, 107, 53, 1), inset 0 0 30px rgba(255, 255, 255, 0.2);
                background-position: 100% 50%;
            }
        }

        @keyframes spin-shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .spin-btn:hover:not(:disabled) {
            background: linear-gradient(145deg, #ff7b45, #ff5500, #ff7b45);
            transform: translateY(-3px);
            box-shadow: 0 5px 35px rgba(255, 107, 53, 0.8);
        }

        .bet-display {
            background: rgba(0,0,0,0.7);
            padding: 10px 20px;
            border-radius: 10px;
            border: 2px solid #ffd700;
            text-align: center;
        }

        .bet-label {
            font-size: 0.8rem;
            color: #ccc;
            margin-bottom: 5px;
        }

        .bet-value {
            font-size: 1.2rem;
            color: #ffd700;
            font-weight: bold;
        }

        .bonus-displays {
            position: fixed;
            top: 50%;
            transform: translateY(-50%);
            z-index: 100;
        }

        .multiplier-display {
            left: 20px;
            background: rgba(0,0,0,0.9);
            border: 2px solid #ffd700;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .freespins-display {
            right: 20px;
            background: rgba(138, 43, 226, 0.9);
            border: 2px solid #dda0dd;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .bonus-text {
            font-size: 0.9rem;
            color: #ccc;
            margin-bottom: 5px;
        }

        .bonus-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #ffd700;
        }

        .freespins-display .bonus-value {
            color: #dda0dd;
        }

        .win-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.95);
            border: 3px solid #ffd700;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            z-index: 1000;
            box-shadow: 0 0 50px rgba(255, 215, 0, 0.8);
        }

        .win-amount {
            font-size: 3rem;
            color: #ffd700;
            font-weight: 900;
            margin-bottom: 15px;
            animation: win-glow 1s ease-in-out infinite alternate;
        }

        .win-type {
            font-size: 1.5rem;
            color: #ff6b35;
            font-weight: bold;
        }

        .message {
            position: fixed;
            top: 20%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.9);
            color: #ffd700;
            padding: 20px 40px;
            border-radius: 10px;
            border: 2px solid #ffd700;
            font-size: 1.2rem;
            font-weight: bold;
            z-index: 1500;
            text-align: center;
        }

        .hidden {
            display: none !important;
        }

        @keyframes spin-blur {
            0% {
                filter: blur(0px);
                transform: scale(1) rotateY(0deg);
                opacity: 1;
            }
            25% {
                filter: blur(2px);
                transform: scale(0.9) rotateY(90deg);
                opacity: 0.8;
            }
            50% {
                filter: blur(4px);
                transform: scale(0.85) rotateY(180deg);
                opacity: 0.6;
            }
            75% {
                filter: blur(2px);
                transform: scale(0.9) rotateY(270deg);
                opacity: 0.8;
            }
            100% {
                filter: blur(0px);
                transform: scale(1) rotateY(360deg);
                opacity: 1;
            }
        }

        @keyframes win-glow {
            0% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
            100% { box-shadow: 0 0 40px rgba(255, 215, 0, 1), 0 0 60px rgba(255, 107, 53, 0.8); }
        }

        @keyframes drop-in {
            0% { transform: translateY(-150px) rotate(180deg); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateY(0) rotate(0deg); opacity: 1; }
        }

        @media (max-width: 768px) {
            .reels {
                grid-template-columns: repeat(5, 70px);
                grid-template-rows: repeat(4, 70px);
                gap: 5px;
            }
            .symbol {
                width: 70px;
                height: 70px;
                font-size: 1.5rem;
            }
            .controls {
                flex-direction: column;
                gap: 15px;
            }
            .stats {
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="header">
            <h1 class="title">VEGAS ACE SLOTS</h1>
            <div class="stats">
                <div class="stat">
                    <div class="stat-label">BALANCE</div>
                    <div class="stat-value" id="balance">$1000.00</div>
                </div>
                <div class="stat">
                    <div class="stat-label">BET</div>
                    <div class="stat-value" id="current-bet">$1.00</div>
                </div>
                <div class="stat">
                    <div class="stat-label">LAST WIN</div>
                    <div class="stat-value" id="last-win">$0.00</div>
                </div>
                <div class="stat multiplier-stat" id="header-multiplier">
                    <div class="stat-label">MULTIPLIER</div>
                    <div class="stat-value multiplier-value" id="header-multiplier-value">1X</div>
                </div>
            </div>
        </div>

        <div class="game-area">
            <div class="slot-machine">
                <div class="reels" id="reels">
                    <!-- Symbols will be generated here -->
                </div>
            </div>
        </div>

        <div class="controls">
            <div class="bet-section">
                <button class="btn" id="bet-down">BET -</button>
                <div class="bet-display">
                    <div class="bet-label">CURRENT BET</div>
                    <div class="bet-value" id="bet-display">$1.00</div>
                </div>
                <button class="btn" id="bet-up">BET +</button>
            </div>

            <button class="btn spin-btn" id="spin-button">
                <span id="spin-text">SPIN</span>
            </button>

            <button class="btn" id="max-bet">MAX BET</button>

            <button class="btn" id="music-toggle">🎼 MUSIC ON</button>
        </div>

        <!-- Bonus Displays -->
        <div class="bonus-displays">
            <div class="multiplier-display hidden" id="multiplier-display">
                <div class="bonus-text">MULTIPLIER</div>
                <div class="bonus-value" id="multiplier-value">x1</div>
            </div>
            <div class="freespins-display hidden" id="freespins-display">
                <div class="bonus-text">FREE SPINS</div>
                <div class="bonus-value" id="freespins-value">0</div>
            </div>
        </div>

        <!-- Win Popup -->
        <div class="win-popup hidden" id="win-popup">
            <div class="win-amount" id="win-amount-display">$0.00</div>
            <div class="win-type" id="win-type-display">WIN!</div>
        </div>

        <!-- Message Display -->
        <div class="message hidden" id="message-display"></div>
    </div>

    <!-- Simple HTML5 Audio System -->
    <audio id="bgMusic" loop>
        <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
    </audio>
    <audio id="spinSound">
        <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
    </audio>
    <audio id="winSound">
        <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
    </audio>
    <script>
        // Game State
        const gameState = {
            balance: 1000.00,
            currentBet: 1.00,
            isSpinning: false,
            inFreeSpins: false,
            freeSpinsRemaining: 0,
            currentMultiplier: 1,
            lastWin: 0,
            consecutiveWins: 0,
            isCoolingSpin: false
        };

        // Advanced Casino Configuration - Modern Slot Logic
        const config = {
            reels: 5,
            rows: 4,
            symbols: ['9', '10', 'J', 'Q', 'K', 'A', '⭐', '🃏'],
            betLevels: [0.10, 0.25, 0.50, 1.00, 2.00, 5.00, 10.00, 25.00, 50.00, 100.00],

            // Multiple Virtual Reel Sets - Optimized for frequent small wins and LDW
            reelSets: {
                normal: [32, 30, 32, 32, 28, 22, 1.5, 5],      // More J,Q for frequent small wins
                generous: [28, 26, 30, 30, 32, 28, 2.5, 8],    // Even more medium symbols
                retention: [22, 22, 28, 25, 38, 35, 4.0, 12],  // Maximum small wins
                bonus: [38, 33, 28, 23, 18, 13, 0.3, 4],       // Suppress bonuses temporarily
                freespins: [20, 18, 25, 22, 45, 40, 0, 15]     // Free spins mode - more premium symbols
            },

            // Balanced payouts - Natural casino feel (like Super Ace)
            payouts: {
                '9': [0, 0, 0.2, 0.8, 2.0],     // Low but fair
                '10': [0, 0, 0.3, 1.0, 2.5],    // Slightly better
                'J': [0, 0, 0.4, 1.5, 4.0],     // Medium-low
                'Q': [0, 0, 0.6, 2.0, 6.0],     // Medium
                'K': [0, 0, 0.8, 3.0, 8.0],     // Good
                'A': [0, 0, 1.2, 5.0, 15.0],    // Premium
                '⭐': [0, 0, 0, 0, 0],           // Scatter - no line pays
                '🃏': [0, 0, 2.0, 8.0, 25.0]    // Wild - best symbol
            },

            // Psychological color scheme
            colors: {
                '9': '#8B4513', '10': '#CD853F', 'J': '#4169E1', 'Q': '#9932CC',
                'K': '#DC143C', 'A': '#FFD700', '⭐': '#FF1493', '🃏': '#00FF00'
            },

            // Advanced Casino Mechanics
            targetRTP: 0.85, // 85% RTP target
            adaptiveRange: 0.05, // ±5% RTP adjustment range
            nearMissChance: 0.25, // 25% chance for near-miss psychology
            maxMultiplier: 5, // Limited to 5x

            // Adaptive Bonus Control - More frequent small celebrations
            maxSpinsWithoutBonus: 120, // Force bonus after 120 spins
            minSpinsBetweenBigWins: 15, // Prevent clustering
            ldwCelebrationThreshold: 0.15, // Celebrate wins > 15% of bet (more frequent)

            // Free Spins Control - Enhanced with auto-play and guaranteed returns
            freeSpinsBase: 10,
            freeSpinsRetrigger: 5,
            maxRetriggers: 1,
            freeSpinsMinReturn: 8.0, // Minimum 8x bet return
            freeSpinsMaxReturn: 40.0, // Maximum 40x bet return
            freeSpinsMinBalanceReturn: 0.30, // Minimum 30% of starting balance return
            autoPlaySpeed: 800, // Auto-play speed in milliseconds
            specialEffectDuration: 2000 // Special effects duration
        };

        let currentBetIndex = 3; // Start at $1.00
        let symbolGrid = [];

        // Advanced Session Tracking for Adaptive RTP
        const sessionData = {
            totalSpins: 0,
            totalWagered: 0,
            totalWon: 0,
            netLoss: 0,
            consecutiveLosses: 0,
            lastBigWin: 0,
            spinsWithoutScatter: 0,
            freeSpinsRetriggered: 0,
            sessionStartBalance: 1000,
            nearMissCount: 0,
            smallWinStreak: 0,
            spinsWithoutWin: 0,
            lastScatterSpin: 0,
            freeSpinsWinTotal: 0,

            // Advanced Tracking
            lastBigWinSpin: -999,
            currentSessionRTP: 0,
            recentWinHistory: [], // Last 20 spins win/loss
            ldwCount: 0, // Loss-Disguised-as-Win count
            nearMissStreak: 0,
            bonusSuppressionActive: false,
            currentReelSet: 'normal',
            adaptiveAdjustments: 0,

            // Enhanced Free Spins Tracking
            isAutoPlaying: false,
            autoPlayRemaining: 0,
            freeSpinsStartBalance: 0,
            guaranteedReturn: 0,
            currentFreeSpinWins: 0
        };

        // Player financial analysis for dynamic adjustments
        function getPlayerFinancialState() {
            const balancePercentage = gameState.balance / sessionData.sessionStartBalance;
            const netLossPercentage = sessionData.netLoss / sessionData.sessionStartBalance;

            return {
                balancePercentage: balancePercentage,
                netLossPercentage: netLossPercentage,
                isLowBalance: balancePercentage < 0.3, // Less than 30% of starting balance
                isCriticalBalance: balancePercentage < 0.15, // Less than 15% of starting balance
                hasLostSignificant: netLossPercentage > 0.4, // Lost more than 40% of starting balance
                needsWin: sessionData.spinsWithoutWin > 8 || sessionData.consecutiveLosses > 5, // More frequent small wins
                needsScatter: sessionData.spinsWithoutScatter > 60 && balancePercentage < 0.5
            };
        }

        // Advanced Adaptive RTP System
        function calculateAdaptiveRTP() {
            if (sessionData.totalSpins < 10) return config.targetRTP;

            sessionData.currentSessionRTP = sessionData.totalWon / sessionData.totalWagered;
            const rtpDeviation = sessionData.currentSessionRTP - config.targetRTP;

            // If player is winning too much, slightly reduce RTP
            // If player is losing too much, slightly increase RTP
            let adaptiveRTP = config.targetRTP;

            if (Math.abs(rtpDeviation) > config.adaptiveRange) {
                if (rtpDeviation > 0) {
                    // Player winning too much - reduce RTP slightly
                    adaptiveRTP = config.targetRTP - (config.adaptiveRange * 0.5);
                    sessionData.bonusSuppressionActive = true;
                } else {
                    // Player losing too much - increase RTP slightly
                    adaptiveRTP = config.targetRTP + (config.adaptiveRange * 0.5);
                    sessionData.bonusSuppressionActive = false;
                }
                sessionData.adaptiveAdjustments++;
            }

            return adaptiveRTP;
        }

        // Dynamic Reel Set Selection - Favor small wins more often
        function selectOptimalReelSet() {
            const playerState = getPlayerFinancialState();
            const currentRTP = calculateAdaptiveRTP();

            // Special reel set for free spins - use premium symbols for big wins
            if (gameState.inFreeSpins) {
                sessionData.currentReelSet = 'freespins'; // Premium symbols for free spins
                console.log(`Free spins mode: Using premium reel set for big wins with 5x multipliers`);
                return config.reelSets[sessionData.currentReelSet];
            }

            // Choose reel set based on player state and RTP needs
            if (sessionData.bonusSuppressionActive) {
                sessionData.currentReelSet = 'bonus'; // Suppress bonuses
            } else if (playerState.isCriticalBalance || currentRTP < config.targetRTP) {
                sessionData.currentReelSet = 'retention'; // Player retention mode
            } else if (playerState.needsWin || sessionData.consecutiveLosses > 5) { // Reduced from 8 to 5
                sessionData.currentReelSet = 'generous'; // More generous
            } else if (sessionData.spinsWithoutWin > 8) { // More frequent generous mode
                sessionData.currentReelSet = 'generous'; // Give small wins more often
            } else {
                sessionData.currentReelSet = 'normal'; // Standard play
            }

            console.log(`Selected reel set: ${sessionData.currentReelSet}, RTP: ${currentRTP.toFixed(3)}`);
            return config.reelSets[sessionData.currentReelSet];
        }

        // Calculate guaranteed return for free spins - minimum 30% of starting balance
        function calculateGuaranteedFreeSpinsReturn() {
            const playerState = getPlayerFinancialState();
            const baseBet = gameState.currentBet;

            // Calculate minimum return based on starting balance (30%)
            const balanceBasedReturn = sessionData.sessionStartBalance * config.freeSpinsMinBalanceReturn;

            // Calculate bet-based return
            let betBasedMultiplier;
            if (playerState.isCriticalBalance) {
                betBasedMultiplier = config.freeSpinsMaxReturn * 0.9; // 90% of max
            } else if (playerState.isLowBalance) {
                betBasedMultiplier = config.freeSpinsMaxReturn * 0.7; // 70% of max
            } else if (playerState.hasLostSignificant) {
                betBasedMultiplier = config.freeSpinsMaxReturn * 0.5; // 50% of max
            } else {
                betBasedMultiplier = config.freeSpinsMinReturn + Math.random() *
                    (config.freeSpinsMaxReturn * 0.4); // Min to 40% of max
            }

            const betBasedReturn = baseBet * betBasedMultiplier;

            // Use the higher of the two calculations
            const guaranteedReturn = Math.max(balanceBasedReturn, betBasedReturn);

            console.log(`Free spins guaranteed return: $${guaranteedReturn.toFixed(2)} (Balance: $${balanceBasedReturn.toFixed(2)}, Bet: $${betBasedReturn.toFixed(2)})`);

            return guaranteedReturn;
        }

        // 🎵 SIMPLE HTML5 AUDIO SYSTEM 🎵
        // ✅ Guaranteed to work in all browsers
        const audioSystem = {
            // Get audio elements
            bgMusic: document.getElementById('bgMusic'),
            spinSound: document.getElementById('spinSound'),
            winSound: document.getElementById('winSound'),
            musicEnabled: true,
            isInitialized: false,

            // Simple initialization
            async init() {
                console.log('🎵 Initializing Simple Audio System...');
                this.isInitialized = true;

                // Set volumes
                if (this.bgMusic) this.bgMusic.volume = 0.3;
                if (this.spinSound) this.spinSound.volume = 0.7;
                if (this.winSound) this.winSound.volume = 0.8;

                console.log('✅ Simple Audio System Ready!');
            },

            // Play background music
            startBackgroundMusic() {
                if (!this.musicEnabled || !this.bgMusic) return;
                try {
                    this.bgMusic.currentTime = 0;
                    this.bgMusic.play().then(() => {
                        console.log('🎼 ✅ Background music playing!');
                    }).catch(e => {
                        console.log('🎼 ❌ Background music failed:', e);
                    });
                } catch (e) {
                    console.log('🎼 ❌ Background music error:', e);
                }
            },

            // Stop background music
            stopBackgroundMusic() {
                if (this.bgMusic) {
                    this.bgMusic.pause();
                    this.bgMusic.currentTime = 0;
                    console.log('🎼 Background music stopped');
                }
            },

            // Play spin sound
            playSpinButtonSound() {
                if (!this.musicEnabled || !this.spinSound) return;
                try {
                    this.spinSound.currentTime = 0;
                    this.spinSound.play().then(() => {
                        console.log('🎛️ ✅ Spin sound playing!');
                    }).catch(e => {
                        console.log('🎛️ ❌ Spin sound failed:', e);
                    });
                } catch (e) {
                    console.log('🎛️ ❌ Spin sound error:', e);
                }
            },

            // Play win sound
            playWinSound(winAmount, betAmount, multiplier = 1) {
                if (!this.musicEnabled || !this.winSound) return;
                try {
                    this.winSound.currentTime = 0;
                    this.winSound.play().then(() => {
                        console.log('🎰 ✅ Win sound playing!');
                    }).catch(e => {
                        console.log('🎰 ❌ Win sound failed:', e);
                    });
                } catch (e) {
                    console.log('🎰 ❌ Win sound error:', e);
                }
            },

            // Toggle music
            toggleMusic() {
                this.musicEnabled = !this.musicEnabled;
                if (this.musicEnabled) {
                    this.startBackgroundMusic();
                    console.log('🎵 MUSIC ENABLED!');
                } else {
                    this.stopBackgroundMusic();
                    console.log('🔇 MUSIC DISABLED!');
                }
                return this.musicEnabled;
            },

            // Dummy methods for compatibility
            playReelSpinSound() { this.playSpinButtonSound(); },
            stopReelSpinSound() { },
            playMultiplierSound() { this.playWinSound(); },
            playCheerSound() { this.playWinSound(); },
            playCoinDropSound() { this.playWinSound(); },
            playBonusSound() { this.playWinSound(); },
            playFreeSpinSound() { this.playWinSound(); },
            playScatterSound() { this.playWinSound(); },
            playCardFlipSound() { this.playSpinButtonSound(); },
            playCardMatchSound() { this.playWinSound(); },
            playCardComboSound() { this.playWinSound(); },
            playAnticipation() { this.playSpinButtonSound(); },
            playModernWin() { this.playWinSound(); }
        };

        // Initialize Game with Audio System
        async function initGame() {
            console.log('🎮 Initializing Vegas Ace Slots...');

            // Initialize audio system
            await audioSystem.init();

            createReels();
            populateReels();
            setupEventListeners();
            updateDisplay();

            console.log('✅ Game initialized successfully! Click SPIN to enable audio.');
        }

        // Add event listener to enable audio on first click
        document.addEventListener('click', function enableAudio() {
            if (audioSystem.musicEnabled && audioSystem.bgMusic) {
                audioSystem.startBackgroundMusic();
                console.log('🎵 Audio enabled on first click!');
            }
            // Remove this listener after first click
            document.removeEventListener('click', enableAudio);
        }, { once: true });

        function createReels() {
            const reelsContainer = document.getElementById('reels');
            reelsContainer.innerHTML = '';
            symbolGrid = [];

            for (let reel = 0; reel < config.reels; reel++) {
                symbolGrid[reel] = [];
                for (let row = 0; row < config.rows; row++) {
                    const symbolElement = document.createElement('div');
                    symbolElement.className = 'symbol';
                    symbolElement.id = `symbol-${reel}-${row}`;
                    reelsContainer.appendChild(symbolElement);
                    symbolGrid[reel][row] = null;
                }
            }
        }

        function populateReels() {
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    const symbol = getRandomSymbol();
                    symbolGrid[reel][row] = symbol;
                    updateSymbolDisplay(reel, row, symbol);
                }
            }
        }

        function getRandomSymbol(isScatterAllowed = true, forceNearMiss = false) {
            // Get optimal reel set based on current session state
            const symbolWeights = [...selectOptimalReelSet()];

            // Bonus suppression logic
            if (sessionData.bonusSuppressionActive && !forceNearMiss) {
                symbolWeights[6] *= 0.3; // Reduce scatter chance significantly
                console.log('Bonus suppression active - reducing scatter chance');
            }

            // Guaranteed bonus trigger after max spins
            if (sessionData.spinsWithoutScatter >= config.maxSpinsWithoutBonus && isScatterAllowed) {
                // Force scatter appearance
                symbolWeights[6] += 50;
                console.log('Forcing bonus trigger after max spins');
            }

            // Big win clustering prevention
            const spinsSinceLastBigWin = sessionData.totalSpins - sessionData.lastBigWinSpin;
            if (spinsSinceLastBigWin < config.minSpinsBetweenBigWins) {
                // Reduce high-value symbols temporarily
                symbolWeights[5] *= 0.7; // A
                symbolWeights[7] *= 0.5; // Wild
                console.log('Preventing big win clustering');
            }

            // Near-miss psychology for scatters
            if (forceNearMiss) {
                symbolWeights[6] = 0; // No scatter on near-miss
                symbolWeights[7] += 2; // More wilds for excitement
            }

            if (!isScatterAllowed) {
                symbolWeights[6] = 0; // Force no scatter
            }

            const totalWeight = symbolWeights.reduce((a, b) => a + b, 0);
            let random = Math.random() * totalWeight;

            for (let i = 0; i < config.symbols.length; i++) {
                random -= symbolWeights[i];
                if (random <= 0) {
                    return config.symbols[i];
                }
            }
            return config.symbols[0];
        }

        function updateSymbolDisplay(reel, row, symbol) {
            const element = document.getElementById(`symbol-${reel}-${row}`);
            if (element) {
                element.textContent = symbol;
                element.style.color = config.colors[symbol] || '#FFD700';
            }
        }

        function setupEventListeners() {
            document.getElementById('spin-button').addEventListener('click', () => {
                console.log('Spin button clicked');
                spin();
            });

            document.getElementById('bet-down').addEventListener('click', () => {
                console.log('Bet down clicked');
                decreaseBet();
            });

            document.getElementById('bet-up').addEventListener('click', () => {
                console.log('Bet up clicked');
                increaseBet();
            });

            document.getElementById('max-bet').addEventListener('click', () => {
                console.log('Max bet clicked');
                setMaxBet();
            });

            document.getElementById('music-toggle').addEventListener('click', () => {
                console.log('Music toggle clicked');
                toggleBackgroundMusic();
            });

            // Keyboard controls
            document.addEventListener('keydown', (event) => {
                if (event.code === 'Space') {
                    event.preventDefault();
                    spin();
                }
            });
        }

        // Main Spin Function with Casino Psychology
        async function spin() {
            console.log('Spin function called');

            if (gameState.isSpinning) {
                console.log('Already spinning, ignoring');
                return;
            }

            if (gameState.balance < gameState.currentBet && !gameState.inFreeSpins) {
                showMessage('Insufficient balance!');
                return;
            }

            gameState.isSpinning = true;
            gameState.lastWin = 0;
            gameState.currentMultiplier = 1; // Always start at 1x

            // 🎛️ Play satisfying spin button sound
            audioSystem.playSpinButtonSound();

            // 🎰 Start reel spinning sound for anticipation
            setTimeout(() => {
                audioSystem.playReelSpinSound();
            }, 200); // Slight delay after button click

            // Session tracking
            sessionData.totalSpins++;
            sessionData.spinsWithoutScatter++;
            sessionData.spinsWithoutWin++; // Track spins without any win

            // Deduct bet and track wagering
            if (!gameState.inFreeSpins) {
                gameState.balance -= gameState.currentBet;
                sessionData.totalWagered += gameState.currentBet;
                sessionData.netLoss = sessionData.totalWagered - sessionData.totalWon;
            } else {
                gameState.freeSpinsRemaining--;
            }

            updateDisplay();

            // Animate spinning with actual symbol changes
            await animateSpinning();

            // Generate final result
            generateNewGrid();

            // Start cascade sequence
            await cascadeSequence();

            gameState.isSpinning = false;
            updateDisplay();

            // Check if free spins ended
            if (gameState.inFreeSpins && gameState.freeSpinsRemaining <= 0) {
                gameState.inFreeSpins = false;
                sessionData.isAutoPlaying = false;
                hideElement('freespins-display');

                // Ensure guaranteed return (minimum 30% of starting balance)
                const actualReturn = sessionData.freeSpinsWinTotal;
                const guaranteedReturn = sessionData.guaranteedReturn;

                console.log('Free spins ended - Actual won:', actualReturn, 'Guaranteed:', guaranteedReturn);

                // If actual return is less than guaranteed, add the difference
                if (actualReturn < guaranteedReturn) {
                    const bonusAmount = guaranteedReturn - actualReturn;
                    gameState.balance += bonusAmount;
                    sessionData.totalWon += bonusAmount;
                    sessionData.freeSpinsWinTotal += bonusAmount;

                    showMessage(`Free Spins Complete! Guaranteed Bonus: $${bonusAmount.toFixed(2)}`);
                    console.log('Added guaranteed return bonus:', bonusAmount);
                } else {
                    showMessage('Free Spins Complete! Great wins!');
                }

                // Log final free spins performance
                const totalReturn = sessionData.freeSpinsWinTotal;
                const returnPercentage = (totalReturn / sessionData.sessionStartBalance) * 100;
                console.log(`Free spins total return: $${totalReturn.toFixed(2)} (${returnPercentage.toFixed(1)}% of starting balance)`);

                // Reset free spins tracking
                sessionData.freeSpinsWinTotal = 0;
                sessionData.guaranteedReturn = 0;
                sessionData.currentFreeSpinWins = 0;
            }
        }

        async function animateSpinning() {
            const spinDuration = 2000; // 2 seconds
            const changeInterval = 100; // Change symbols every 100ms
            const totalChanges = spinDuration / changeInterval;

            // Start spinning animation for all symbols
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    const element = document.getElementById(`symbol-${reel}-${row}`);
                    if (element) {
                        element.classList.add('spinning');
                    }
                }
            }

            // Change symbols rapidly during spin
            for (let i = 0; i < totalChanges; i++) {
                for (let reel = 0; reel < config.reels; reel++) {
                    for (let row = 0; row < config.rows; row++) {
                        const randomSymbol = getRandomSymbol();
                        updateSymbolDisplay(reel, row, randomSymbol);
                    }
                }
                await new Promise(resolve => setTimeout(resolve, changeInterval));
            }

            // Remove spinning animation and stop reel sound
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    const element = document.getElementById(`symbol-${reel}-${row}`);
                    if (element) {
                        element.classList.remove('spinning');
                    }
                }
            }

            // 🎰 Stop reel spinning sound - anticipation complete
            audioSystem.stopReelSpinSound();
        }

        function generateNewGrid() {
            // If cooling spin, force no winning combinations
            if (gameState.isCoolingSpin) {
                console.log('Generating cooling spin - no wins allowed');
                generateNoWinGrid();
                return;
            }

            // Advanced near-miss logic
            const shouldNearMiss = Math.random() < config.nearMissChance &&
                                   sessionData.consecutiveLosses > 3 &&
                                   !gameState.inFreeSpins &&
                                   sessionData.spinsWithoutScatter > 20;
            if (shouldNearMiss) {
                audioSystem.playAnticipation();
            }

            // Generate the final grid with psychological patterns
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    let symbol;

                    // Near-miss psychology: 2 scatters visible, third just off-screen
                    if (shouldNearMiss && reel < 2 && row === 1 && Math.random() < 0.4) {
                        symbol = '⭐'; // Place scatter in first 2 reels
                        sessionData.nearMissCount++;
                        sessionData.nearMissStreak++;
                        console.log('Near-miss scatter placed - building anticipation');
                    } else if (shouldNearMiss && reel === 2 && row === 0) {
                        symbol = getRandomSymbol(false, true); // No scatter, but exciting symbol
                        console.log('Near-miss completed - player should feel "almost won"');
                    } else {
                        // Normal symbol generation
                        symbol = getRandomSymbol();
                    }

                    symbolGrid[reel][row] = symbol;
                    updateSymbolDisplay(reel, row, symbol);
                }
            }
        }

        function generateNoWinGrid() {
            // Generate a grid with no winning combinations for cooling spin
            const symbols = ['9', '10', 'J', 'Q', 'K']; // Only low symbols, no wilds/scatters

            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    // Ensure no 3+ consecutive symbols
                    let symbol;
                    do {
                        symbol = symbols[Math.floor(Math.random() * symbols.length)];
                    } while (reel >= 2 &&
                             symbolGrid[reel-1] && symbolGrid[reel-2] &&
                             symbolGrid[reel-1][row] === symbol &&
                             symbolGrid[reel-2][row] === symbol);

                    symbolGrid[reel][row] = symbol;
                    updateSymbolDisplay(reel, row, symbol);
                }
            }
        }

        async function cascadeSequence() {
            // Check if this is a cooling spin (forced loss after 5x)
            if (gameState.isCoolingSpin) {
                console.log('Cooling spin - forcing no wins');
                gameState.isCoolingSpin = false;
                gameState.currentMultiplier = 1;
                gameState.consecutiveWins = 0;
                gameState.lastWin = 0;

                // Track losses for psychology
                sessionData.consecutiveLosses++;
                sessionData.smallWinStreak = 0;

                updateDisplay();
                return; // No wins on cooling spin
            }

            let cascadeCount = 0;
            let hasWins = true;
            let totalWinThisSpin = 0;

            while (hasWins) {
                // Fill empty positions
                await fillEmptyPositions();

                // Check for wins
                const wins = checkWins();
                console.log('Wins found:', wins);

                if (wins.length > 0) {
                    cascadeCount++;

                    // Progressive multiplier system (1x → 5x) - More frequent small multipliers
                    if (cascadeCount === 1) {
                        // First win in this spin
                        gameState.consecutiveWins++;
                        gameState.currentMultiplier = Math.min(gameState.consecutiveWins, config.maxMultiplier);

                        // Small win boost - give players more 1x-3x experiences with sound
                        if (gameState.currentMultiplier <= 3 && sessionData.spinsWithoutWin > 5) {
                            console.log(`Small multiplier hit: ${gameState.currentMultiplier}x - player feels winning!`);
                        }

                        // Play multiplier sound for any multiplier increase
                        if (gameState.currentMultiplier > 1) {
                            audioSystem.playMultiplierSound();
                            console.log(`Multiplier increased to ${gameState.currentMultiplier}x - exciting build-up!`);
                        }

                        // Check if we need to set cooling spin for next round
                        if (gameState.currentMultiplier === 5) {
                            gameState.isCoolingSpin = true;
                            console.log('Reached 5x - next spin will be cooling spin');
                        }
                    }

                    // Track session data
                    sessionData.consecutiveLosses = 0;
                    sessionData.smallWinStreak++;
                    sessionData.spinsWithoutWin = 0; // Reset win drought counter

                    // Calculate win amount
                    const winAmount = calculateWinAmount(wins);
                    gameState.lastWin += winAmount;
                    gameState.balance += winAmount;
                    totalWinThisSpin += winAmount;

                    // Track session wins
                    sessionData.totalWon += winAmount;
                    sessionData.netLoss = sessionData.totalWagered - sessionData.totalWon;

                    // Track big wins for clustering prevention and 5x multiplier usage
                    const winMultiplier = winAmount / gameState.currentBet;
                    if (winMultiplier >= 10) {
                        sessionData.lastBigWinSpin = sessionData.totalSpins;
                        if (gameState.currentMultiplier === 5) {
                            console.log(`BIG WIN with 5x MULTIPLIER! ${winMultiplier.toFixed(1)}x bet using premium cards!`);
                        } else {
                            console.log(`Big win detected: ${winMultiplier.toFixed(1)}x bet`);
                        }
                    }

                    // Loss-Disguised-as-Win (LDW) tracking - More frequent celebrations with EXCITING audio
                    if (winAmount < gameState.currentBet && winAmount >= gameState.currentBet * config.ldwCelebrationThreshold) {
                        sessionData.ldwCount++;
                        console.log(`🎊 LDW CELEBRATION: Won $${winAmount.toFixed(2)} on $${gameState.currentBet.toFixed(2)} bet - PLAYER FEELS LIKE WINNING! 🎊`);

                        // Extra exciting sound for LDW to make player feel amazing
                        setTimeout(() => {
                            audioSystem.playWinSound(winAmount, gameState.currentBet, 1);
                        }, 200);

                        // Add coin drop sound to make LDW feel like real money
                        setTimeout(() => {
                            audioSystem.playCoinDropSound();
                        }, 600);
                    }

                    // Extra celebration for any win during drought
                    if (sessionData.spinsWithoutWin > 10 && winAmount > 0) {
                        console.log(`🎯 DROUGHT BREAKER WIN: $${winAmount.toFixed(2)} - PLAYER RELIEF AND EXCITEMENT! 🎯`);

                        // Play cheer sound for drought breaker
                        setTimeout(() => {
                            audioSystem.playCheerSound();
                        }, 500);

                        // Add extra coin drop for drought breaker
                        setTimeout(() => {
                            audioSystem.playCoinDropSound();
                        }, 800);
                    }

                    // Track free spins wins for balanced returns
                    if (gameState.inFreeSpins) {
                        sessionData.freeSpinsWinTotal += winAmount;
                    }

                    // Update recent win history (last 20 spins)
                    sessionData.recentWinHistory.push(winAmount);
                    if (sessionData.recentWinHistory.length > 20) {
                        sessionData.recentWinHistory.shift();
                    }

                    console.log('Win amount:', winAmount, 'Multiplier:', gameState.currentMultiplier);

                    // Show multiplier if > 1
                    if (gameState.currentMultiplier > 1) {
                        showElement('multiplier-display');
                        document.getElementById('multiplier-value').textContent = `x${gameState.currentMultiplier}`;
                    }

                    // Highlight wins
                    await highlightWins(wins);

                    // Remove winning symbols
                    await removeWinningSymbols(wins);

                    // Check for bonus triggers
                    checkBonusTriggers(wins);

                    updateDisplay();
                } else {
                    hasWins = false;
                }
            }

            // Handle no wins - reset multiplier progression
            if (totalWinThisSpin === 0) {
                gameState.consecutiveWins = 0;
                gameState.currentMultiplier = 1;
                sessionData.consecutiveLosses++;
                sessionData.smallWinStreak = 0;
                sessionData.spinsWithoutWin++;

                // Update recent win history with loss
                sessionData.recentWinHistory.push(0);
                if (sessionData.recentWinHistory.length > 20) {
                    sessionData.recentWinHistory.shift();
                }

                console.log('No wins - reset multiplier to 1x');
            }

            // Show win celebration with enhanced audio
            if (gameState.lastWin > 0) {
                audioSystem.playModernWin(gameState.lastWin, gameState.currentBet);
                showWinCelebration(gameState.lastWin);

                // Extra cheer sound for big wins
                const winMultiplier = gameState.lastWin / gameState.currentBet;
                if (winMultiplier >= 15) {
                    setTimeout(() => {
                        audioSystem.playCheerSound();
                    }, 1000);
                }
            }

            // Hide multiplier if back to base
            if (gameState.currentMultiplier <= 1) {
                hideElement('multiplier-display');
            }
        }

        async function fillEmptyPositions() {
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = config.rows - 1; row >= 0; row--) {
                    if (!symbolGrid[reel][row]) {
                        const symbol = getRandomSymbol();
                        symbolGrid[reel][row] = symbol;

                        const element = document.getElementById(`symbol-${reel}-${row}`);
                        if (element) {
                            element.classList.add('dropping');
                            updateSymbolDisplay(reel, row, symbol);

                            setTimeout(() => {
                                element.classList.remove('dropping');
                            }, 800);
                        }
                    }
                }
            }

            await new Promise(resolve => setTimeout(resolve, 500));
        }

        function checkWins() {
            const wins = [];

            // Check ways-to-win (left to right)
            for (let row = 0; row < config.rows; row++) {
                let count = 1;
                let currentSymbol = symbolGrid[0][row];
                let positions = [{reel: 0, row: row}];

                if (!currentSymbol || currentSymbol === '⭐') continue;

                for (let reel = 1; reel < config.reels; reel++) {
                    const symbol = symbolGrid[reel][row];
                    if (symbol === currentSymbol || symbol === '🃏') {
                        count++;
                        positions.push({reel: reel, row: row});
                    } else {
                        break;
                    }
                }

                if (count >= 3) {
                    wins.push({
                        symbol: currentSymbol,
                        count: count,
                        positions: positions
                    });

                    // Play card match sounds for card symbols in line wins (like Super Ace)
                    if (['J', 'Q', 'K', 'A'].includes(currentSymbol)) {
                        if (count >= 5) {
                            // Special combo sound for 5 cards in a line
                            setTimeout(() => {
                                audioSystem.playCardComboSound(currentSymbol, count);
                            }, 400);
                        } else if (count >= 4) {
                            // Card combo sound for 4 cards
                            setTimeout(() => {
                                audioSystem.playCardComboSound(currentSymbol, count);
                            }, 350);
                        } else {
                            // Regular card match sound for 3 cards
                            setTimeout(() => {
                                audioSystem.playCardMatchSound(currentSymbol, count);
                            }, 300);
                        }
                    }
                }
            }

            // Check for scatter wins
            const scatterPositions = [];
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    if (symbolGrid[reel][row] === '⭐') {
                        scatterPositions.push({reel, row});
                    }
                }
            }

            if (scatterPositions.length >= 3) {
                wins.push({
                    symbol: '⭐',
                    count: scatterPositions.length,
                    positions: scatterPositions
                });

                // 🃏 Play scatter sound - rare and exciting
                setTimeout(() => {
                    audioSystem.playScatterSound();
                }, 100);
            }

            // Additional ChatGPT-style middle row count-based wins for frequent small wins
            const middleRowWins = checkMiddleRowCountWins();
            wins.push(...middleRowWins);

            return wins;
        }

        // ChatGPT-inspired middle row count-based win system for more frequent small wins
        function checkMiddleRowCountWins() {
            const wins = [];
            const middleRow = [];

            // Get middle row (row index 1 for 4-row grid)
            for (let reel = 0; reel < config.reels; reel++) {
                middleRow.push(symbolGrid[reel][1]);
            }

            // Count occurrences of each symbol
            const symbolCounts = {};
            middleRow.forEach(symbol => {
                if (symbol !== '⭐') { // Exclude scatters from count wins
                    symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
                }
            });

            // Check for 3+ same symbols (ChatGPT style) - gives more frequent small wins
            for (const [symbol, count] of Object.entries(symbolCounts)) {
                if (count >= 3) {
                    // Create positions for middle row
                    const positions = [];
                    for (let reel = 0; reel < config.reels; reel++) {
                        if (symbolGrid[reel][1] === symbol) {
                            positions.push({reel, row: 1});
                        }
                    }

                    wins.push({
                        symbol: symbol,
                        count: count,
                        positions: positions,
                        type: 'middle-row-count'
                    });

                    // Play card match sounds for card symbols (like Super Ace)
                    if (['J', 'Q', 'K', 'A'].includes(symbol)) {
                        if (count >= 4) {
                            // Special combo sound for 4+ cards
                            setTimeout(() => {
                                audioSystem.playCardComboSound(symbol, count);
                            }, 200);
                        } else {
                            // Regular card match sound for 3 cards
                            setTimeout(() => {
                                audioSystem.playCardMatchSound(symbol, count);
                            }, 200);
                        }
                    }

                    console.log(`ChatGPT-style count win: ${count}x ${symbol} in middle row - frequent small win!`);
                }
            }

            return wins;
        }

        function calculateWinAmount(wins) {
            let totalWin = 0;

            wins.forEach(win => {
                const payout = config.payouts[win.symbol];
                if (payout && payout[win.count]) {
                    totalWin += payout[win.count] * gameState.currentBet;
                }
            });

            return totalWin * gameState.currentMultiplier;
        }

        async function highlightWins(wins) {
            // Clear previous highlights
            document.querySelectorAll('.symbol').forEach(el => {
                el.classList.remove('winning');
            });

            // Highlight winning symbols
            wins.forEach(win => {
                win.positions.forEach(pos => {
                    const element = document.getElementById(`symbol-${pos.reel}-${pos.row}`);
                    if (element) {
                        element.classList.add('winning');
                    }
                });
            });

            await new Promise(resolve => setTimeout(resolve, 1500));
        }

        async function removeWinningSymbols(wins) {
            const positionsToRemove = new Set();

            wins.forEach(win => {
                win.positions.forEach(pos => {
                    positionsToRemove.add(`${pos.reel}-${pos.row}`);
                });
            });

            // Remove symbols
            positionsToRemove.forEach(posKey => {
                const [reel, row] = posKey.split('-').map(Number);
                symbolGrid[reel][row] = null;

                const element = document.getElementById(`symbol-${reel}-${row}`);
                if (element) {
                    element.textContent = '';
                    element.classList.remove('winning');
                }
            });

            await new Promise(resolve => setTimeout(resolve, 300));
        }

        function checkBonusTriggers(wins) {
            const scatterWin = wins.find(win => win.symbol === '⭐');
            if (scatterWin && scatterWin.count >= 3) {
                sessionData.spinsWithoutScatter = 0; // Reset counter

                if (!gameState.inFreeSpins) {
                    triggerFreeSpins(scatterWin.count);
                } else if (sessionData.freeSpinsRetriggered < config.maxRetriggers) {
                    retriggerFreeSpins();
                }
            }
        }

        function triggerFreeSpins(scatterCount) {
            // Always 10 free spins regardless of scatter count (casino style)
            const spinsAwarded = config.freeSpinsBase;
            gameState.inFreeSpins = true;
            gameState.freeSpinsRemaining = spinsAwarded;
            sessionData.freeSpinsRetriggered = 0; // Reset retrigger count
            sessionData.freeSpinsWinTotal = 0; // Reset free spins win tracking

            // Set up auto-play and guaranteed return
            sessionData.isAutoPlaying = true;
            sessionData.autoPlayRemaining = spinsAwarded;
            sessionData.freeSpinsStartBalance = gameState.balance;
            sessionData.guaranteedReturn = calculateGuaranteedFreeSpinsReturn();
            sessionData.currentFreeSpinWins = 0;

            showElement('freespins-display');
            document.getElementById('freespins-value').textContent = spinsAwarded;

            // Play bonus sound for free spins
            audioSystem.playBonusSound();

            showMessage(`${spinsAwarded} FREE SPINS AWARDED! AUTO-PLAYING WITH SPECIAL EFFECTS!`);

            // Log player state when free spins trigger
            const playerState = getPlayerFinancialState();
            console.log('Free spins triggered - Player balance:', gameState.balance, 'Guaranteed return:', sessionData.guaranteedReturn);

            // Start auto-play sequence
            setTimeout(() => {
                autoPlayFreeSpins();
            }, config.specialEffectDuration);
        }

        // Auto-play free spins with special effects
        async function autoPlayFreeSpins() {
            if (!sessionData.isAutoPlaying || gameState.freeSpinsRemaining <= 0) {
                return;
            }

            console.log(`Auto-playing free spin ${11 - gameState.freeSpinsRemaining}/10 with premium symbols`);

            // Play free spin sound for each auto-play spin
            audioSystem.playFreeSpinSound();

            // Trigger a spin automatically
            await spin();

            // Continue auto-play if more spins remain
            if (gameState.freeSpinsRemaining > 0 && sessionData.isAutoPlaying) {
                setTimeout(() => {
                    autoPlayFreeSpins();
                }, config.autoPlaySpeed);
            }
        }

        function retriggerFreeSpins() {
            // Only +5 spins and max 1 retrigger
            gameState.freeSpinsRemaining += config.freeSpinsRetrigger;
            sessionData.freeSpinsRetriggered++;
            sessionData.autoPlayRemaining += config.freeSpinsRetrigger;

            document.getElementById('freespins-value').textContent = gameState.freeSpinsRemaining;
            showMessage(`+${config.freeSpinsRetrigger} FREE SPINS!`);
        }

        // Betting Functions
        function decreaseBet() {
            if (gameState.isSpinning || gameState.inFreeSpins) return;

            if (currentBetIndex > 0) {
                currentBetIndex--;
                gameState.currentBet = config.betLevels[currentBetIndex];
                updateDisplay();
            }
        }

        function increaseBet() {
            if (gameState.isSpinning || gameState.inFreeSpins) return;

            if (currentBetIndex < config.betLevels.length - 1 && gameState.balance >= config.betLevels[currentBetIndex + 1]) {
                currentBetIndex++;
                gameState.currentBet = config.betLevels[currentBetIndex];
                updateDisplay();
            }
        }

        function setMaxBet() {
            if (gameState.isSpinning || gameState.inFreeSpins) return;

            // Find highest affordable bet
            let maxIndex = 0;
            for (let i = config.betLevels.length - 1; i >= 0; i--) {
                if (gameState.balance >= config.betLevels[i]) {
                    maxIndex = i;
                    break;
                }
            }

            currentBetIndex = maxIndex;
            gameState.currentBet = config.betLevels[currentBetIndex];
            updateDisplay();
            console.log('Max bet set to:', gameState.currentBet);
        }

        // 🎵 Howler.js Music Toggle Functionality - Complete Audio Control
        function toggleBackgroundMusic() {
            const musicButton = document.getElementById('music-toggle');

            // Use the audioSystem's built-in toggle method
            const isEnabled = audioSystem.toggleMusic();

            if (isEnabled) {
                musicButton.textContent = '🎼 MUSIC ON';
                console.log('🎵 HOWLER.JS MUSIC ENABLED - All casino sounds active!');
            } else {
                musicButton.textContent = '🔇 MUSIC OFF';
                console.log('🔇 HOWLER.JS MUSIC DISABLED - Silent mode');
            }
        }

        // UI Functions
        function updateDisplay() {
            document.getElementById('balance').textContent = `$${gameState.balance.toFixed(2)}`;
            document.getElementById('current-bet').textContent = `$${gameState.currentBet.toFixed(2)}`;
            document.getElementById('bet-display').textContent = `$${gameState.currentBet.toFixed(2)}`;
            document.getElementById('last-win').textContent = `$${gameState.lastWin.toFixed(2)}`;

            // Update header multiplier display
            document.getElementById('header-multiplier-value').textContent = `${gameState.currentMultiplier}X`;

            const spinButton = document.getElementById('spin-button');
            const spinText = document.getElementById('spin-text');

            if (gameState.isSpinning) {
                spinButton.disabled = true;
                spinText.textContent = 'SPINNING...';
            } else if (gameState.inFreeSpins) {
                spinButton.disabled = false;
                spinText.textContent = `FREE SPIN (${gameState.freeSpinsRemaining})`;
            } else {
                spinButton.disabled = gameState.balance < gameState.currentBet;
                spinText.textContent = 'SPIN';
            }
        }

        function showWinCelebration(amount) {
            const winMultiplier = amount / gameState.currentBet;
            let winType = 'WIN!';

            if (winMultiplier >= 50) {
                winType = 'MEGA WIN!';
            } else if (winMultiplier >= 25) {
                winType = 'SUPER WIN!';
            } else if (winMultiplier >= 10) {
                winType = 'BIG WIN!';
            }

            document.getElementById('win-amount-display').textContent = `$${amount.toFixed(2)}`;
            document.getElementById('win-type-display').textContent = winType;
            showElement('win-popup');

            setTimeout(() => {
                hideElement('win-popup');
            }, 3000);
        }

        function showMessage(text) {
            const messageEl = document.getElementById('message-display');
            messageEl.textContent = text;
            showElement('message-display');

            setTimeout(() => {
                hideElement('message-display');
            }, 3000);
        }

        function showElement(id) {
            document.getElementById(id).classList.remove('hidden');
        }

        function hideElement(id) {
            document.getElementById(id).classList.add('hidden');
        }

        // Start the game when page loads
        window.addEventListener('load', () => {
            console.log('Page loaded, starting game...');
            initGame();
        });
    </script>
</body>
</html>

