<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vegas Ace Slots - Working Version</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', monospace;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a0a0a 25%, #2a1a0a 50%, #1a0a1a 75%, #0a0a2a 100%);
            color: #fff;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            animation: subtle-pulse 4s ease-in-out infinite alternate;
        }

        @keyframes subtle-pulse {
            0% { background-size: 100% 100%; }
            100% { background-size: 105% 105%; }
        }

        .game-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .header {
            text-align: center;
            padding: 25px;
            background: linear-gradient(145deg, rgba(255,215,0,0.1), rgba(255,69,0,0.1));
            border-radius: 20px;
            margin-bottom: 25px;
            border: 3px solid #ffd700;
            box-shadow: 0 0 30px rgba(255, 215, 0, 0.3), inset 0 0 20px rgba(255, 69, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,215,0,0.1), transparent);
            animation: shine 3s linear infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .title {
            font-size: 3rem;
            color: #ffd700;
            margin-bottom: 20px;
            text-shadow: 0 0 30px rgba(255, 215, 0, 0.8), 0 0 60px rgba(255, 69, 0, 0.4);
            font-weight: 900;
            letter-spacing: 3px;
            position: relative;
            z-index: 1;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 40px;
            flex-wrap: wrap;
        }

        .stat {
            text-align: center;
            background: linear-gradient(145deg, rgba(0,0,0,0.8), rgba(20,20,20,0.9));
            padding: 15px 25px;
            border-radius: 15px;
            border: 2px solid #ffd700;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.2), inset 0 0 10px rgba(255, 215, 0, 0.1);
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
        }

        .stat:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 25px rgba(255, 215, 0, 0.4);
        }

        .stat-label {
            font-size: 1rem;
            color: #ffd700;
            margin-bottom: 8px;
            font-weight: bold;
            letter-spacing: 1px;
        }

        .stat-value {
            font-size: 1.6rem;
            color: #fff;
            font-weight: 900;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .multiplier-stat {
            background: linear-gradient(145deg, rgba(255,69,0,0.8), rgba(255,140,0,0.9));
            border-color: #ff6b35;
            box-shadow: 0 0 25px rgba(255, 69, 0, 0.4), inset 0 0 15px rgba(255, 140, 0, 0.2);
        }

        .multiplier-value {
            color: #fff;
            font-size: 1.8rem;
            text-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
            animation: multiplier-glow 1.5s ease-in-out infinite alternate;
        }

        @keyframes multiplier-glow {
            0% {
                text-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
                transform: scale(1);
            }
            100% {
                text-shadow: 0 0 25px rgba(255, 255, 255, 1), 0 0 35px rgba(255, 69, 0, 0.8);
                transform: scale(1.05);
            }
        }

        .game-area {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
        }

        .slot-machine {
            background: linear-gradient(145deg, rgba(0,0,0,0.9), rgba(30,30,30,0.9));
            border: 4px solid #ffd700;
            border-radius: 25px;
            padding: 40px;
            box-shadow:
                0 0 60px rgba(255, 215, 0, 0.4),
                inset 0 0 30px rgba(255, 69, 0, 0.1),
                0 10px 30px rgba(0, 0, 0, 0.5);
            position: relative;
        }

        .slot-machine::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #ffd700, #ff6b35, #ffd700, #ff6b35);
            border-radius: 25px;
            z-index: -1;
            animation: border-glow 2s linear infinite;
        }

        @keyframes border-glow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .reels {
            display: grid;
            grid-template-columns: repeat(5, 100px);
            grid-template-rows: repeat(4, 100px);
            gap: 10px;
            margin-bottom: 20px;
        }

        .symbol {
            width: 100px;
            height: 100px;
            background: linear-gradient(145deg, #1a1a1a, #0a0a0a);
            border: 3px solid #ffd700;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.2rem;
            font-weight: 900;
            color: #ffd700;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 0 15px rgba(255, 215, 0, 0.3),
                inset 0 0 10px rgba(255, 215, 0, 0.1);
        }

        .symbol::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .symbol:hover::before {
            left: 100%;
        }

        .symbol.spinning {
            animation: spin-blur 0.2s ease-in-out infinite;
            border-color: #ff6b35;
            box-shadow: 0 0 15px rgba(255, 107, 53, 0.6);
        }

        .symbol.winning {
            background: linear-gradient(145deg, #ff6b35, #ff4500);
            box-shadow: 0 0 30px rgba(255, 215, 0, 1);
            animation: win-glow 1s ease-in-out infinite alternate;
            transform: scale(1.1);
        }

        .symbol.dropping {
            animation: drop-in 0.8s ease-out;
        }

        .controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            padding: 20px;
            background: rgba(0,0,0,0.4);
            border-radius: 15px;
            border: 2px solid #ffd700;
            flex-wrap: wrap;
        }

        .bet-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .btn {
            padding: 12px 20px;
            background: linear-gradient(145deg, #2a2a3e, #1a1a2e);
            border: 2px solid #ffd700;
            color: #ffd700;
            font-family: 'Orbitron', monospace;
            font-weight: bold;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .btn:hover:not(:disabled) {
            background: linear-gradient(145deg, #3a3a4e, #2a2a3e);
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.4);
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .spin-btn {
            padding: 20px 40px;
            background: linear-gradient(145deg, #ff6b35, #ff4500, #ff6b35);
            background-size: 200% 200%;
            color: white;
            font-size: 1.5rem;
            font-weight: 900;
            border: 4px solid #ffd700;
            min-width: 180px;
            border-radius: 15px;
            text-shadow: 0 0 10px rgba(0,0,0,0.8);
            box-shadow:
                0 0 30px rgba(255, 107, 53, 0.6),
                inset 0 0 20px rgba(255, 255, 255, 0.1);
            animation: pulse-glow 2s ease-in-out infinite alternate;
            position: relative;
            overflow: hidden;
        }

        .spin-btn::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: spin-shine 3s linear infinite;
        }

        @keyframes pulse-glow {
            0% {
                box-shadow: 0 0 30px rgba(255, 107, 53, 0.6), inset 0 0 20px rgba(255, 255, 255, 0.1);
                background-position: 0% 50%;
            }
            100% {
                box-shadow: 0 0 50px rgba(255, 107, 53, 1), inset 0 0 30px rgba(255, 255, 255, 0.2);
                background-position: 100% 50%;
            }
        }

        @keyframes spin-shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .spin-btn:hover:not(:disabled) {
            background: linear-gradient(145deg, #ff7b45, #ff5500, #ff7b45);
            transform: translateY(-3px);
            box-shadow: 0 5px 35px rgba(255, 107, 53, 0.8);
        }

        .bet-display {
            background: rgba(0,0,0,0.7);
            padding: 10px 20px;
            border-radius: 10px;
            border: 2px solid #ffd700;
            text-align: center;
        }

        .bet-label {
            font-size: 0.8rem;
            color: #ccc;
            margin-bottom: 5px;
        }

        .bet-value {
            font-size: 1.2rem;
            color: #ffd700;
            font-weight: bold;
        }

        .bonus-displays {
            position: fixed;
            top: 50%;
            transform: translateY(-50%);
            z-index: 100;
        }

        .multiplier-display {
            left: 20px;
            background: rgba(0,0,0,0.9);
            border: 2px solid #ffd700;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .freespins-display {
            right: 20px;
            background: rgba(138, 43, 226, 0.9);
            border: 2px solid #dda0dd;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .bonus-text {
            font-size: 0.9rem;
            color: #ccc;
            margin-bottom: 5px;
        }

        .bonus-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #ffd700;
        }

        .freespins-display .bonus-value {
            color: #dda0dd;
        }

        .win-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.95);
            border: 3px solid #ffd700;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            z-index: 1000;
            box-shadow: 0 0 50px rgba(255, 215, 0, 0.8);
        }

        .win-amount {
            font-size: 3rem;
            color: #ffd700;
            font-weight: 900;
            margin-bottom: 15px;
            animation: win-glow 1s ease-in-out infinite alternate;
        }

        .win-type {
            font-size: 1.5rem;
            color: #ff6b35;
            font-weight: bold;
        }

        .message {
            position: fixed;
            top: 20%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.9);
            color: #ffd700;
            padding: 20px 40px;
            border-radius: 10px;
            border: 2px solid #ffd700;
            font-size: 1.2rem;
            font-weight: bold;
            z-index: 1500;
            text-align: center;
        }

        .hidden {
            display: none !important;
        }

        @keyframes spin-blur {
            0% {
                filter: blur(0px);
                transform: scale(1) rotateY(0deg);
                opacity: 1;
            }
            25% {
                filter: blur(2px);
                transform: scale(0.9) rotateY(90deg);
                opacity: 0.8;
            }
            50% {
                filter: blur(4px);
                transform: scale(0.85) rotateY(180deg);
                opacity: 0.6;
            }
            75% {
                filter: blur(2px);
                transform: scale(0.9) rotateY(270deg);
                opacity: 0.8;
            }
            100% {
                filter: blur(0px);
                transform: scale(1) rotateY(360deg);
                opacity: 1;
            }
        }

        @keyframes win-glow {
            0% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
            100% { box-shadow: 0 0 40px rgba(255, 215, 0, 1), 0 0 60px rgba(255, 107, 53, 0.8); }
        }

        @keyframes drop-in {
            0% { transform: translateY(-150px) rotate(180deg); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateY(0) rotate(0deg); opacity: 1; }
        }

        @media (max-width: 768px) {
            .reels {
                grid-template-columns: repeat(5, 70px);
                grid-template-rows: repeat(4, 70px);
                gap: 5px;
            }
            .symbol {
                width: 70px;
                height: 70px;
                font-size: 1.5rem;
            }
            .controls {
                flex-direction: column;
                gap: 15px;
            }
            .stats {
                gap: 20px;
            }
        }
        /* Authentication System Styles */
        .modal {
            display: flex;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: linear-gradient(145deg, #1a1a2e, #16213e);
            border: 3px solid #ffd700;
            border-radius: 20px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 0 50px rgba(255, 215, 0, 0.5);
            position: relative;
        }

        .auth-container h2, .profile-container h2 {
            color: #ffd700;
            text-align: center;
            margin-bottom: 25px;
            font-size: 1.8rem;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .auth-form h3, .profile-container h3 {
            color: #fff;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            color: #ffd700;
            margin-bottom: 8px;
            font-weight: bold;
        }

        .input-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ffd700;
            border-radius: 8px;
            background: rgba(0,0,0,0.7);
            color: #fff;
            font-family: 'Orbitron', monospace;
            font-size: 1rem;
        }

        .input-group input:focus {
            outline: none;
            border-color: #ff6b35;
            box-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
        }

        .auth-btn, .action-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(145deg, #ff6b35, #ff4500);
            border: 2px solid #ffd700;
            color: white;
            font-family: 'Orbitron', monospace;
            font-weight: bold;
            font-size: 1.1rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .auth-btn:hover, .action-btn:hover {
            background: linear-gradient(145deg, #ff7b45, #ff5500);
            box-shadow: 0 0 20px rgba(255, 107, 53, 0.6);
            transform: translateY(-2px);
        }

        .auth-switch {
            text-align: center;
            color: #ccc;
        }

        .auth-switch span {
            color: #ffd700;
            cursor: pointer;
            text-decoration: underline;
        }

        .auth-switch span:hover {
            color: #ff6b35;
        }

        .profile-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .close-btn {
            background: #ff4444;
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }

        .close-btn:hover {
            background: #ff6666;
        }

        .user-details, .balance-section, .activity-section, .transaction-history {
            background: rgba(0,0,0,0.3);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #ffd700;
        }

        .balance-display-large {
            font-size: 2.5rem;
            color: #ffd700;
            text-align: center;
            margin: 15px 0;
            font-weight: bold;
            text-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
        }

        .balance-actions {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .action-btn.deposit {
            background: linear-gradient(145deg, #28a745, #20c997);
        }

        .action-btn.withdraw {
            background: linear-gradient(145deg, #dc3545, #fd7e14);
        }

        .action-btn.cancel {
            background: linear-gradient(145deg, #6c757d, #495057);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background: rgba(255, 215, 0, 0.1);
            border-radius: 5px;
        }

        .stat-label {
            color: #ccc;
        }

        .stat-value {
            color: #ffd700;
            font-weight: bold;
        }

        .transaction-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .transaction-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            border-bottom: 1px solid #333;
            margin-bottom: 5px;
        }

        .transaction-item:last-child {
            border-bottom: none;
        }

        .transaction-type {
            font-weight: bold;
        }

        .transaction-type.deposit {
            color: #28a745;
        }

        .transaction-type.withdraw {
            color: #dc3545;
        }

        .transaction-type.bet {
            color: #ffc107;
        }

        .transaction-type.win {
            color: #17a2b8;
        }

        .no-transactions {
            text-align: center;
            color: #666;
            font-style: italic;
        }

        .payment-methods {
            margin: 20px 0;
        }

        .payment-options {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .payment-option {
            display: flex;
            align-items: center;
            padding: 10px;
            background: rgba(0,0,0,0.3);
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .payment-option:hover {
            background: rgba(255, 215, 0, 0.1);
        }

        .payment-option input {
            margin-right: 10px;
        }

        .current-balance {
            background: rgba(255, 215, 0, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.2rem;
            color: #ffd700;
        }

        .withdrawal-info {
            margin: 20px 0;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-controls {
            display: flex;
            gap: 10px;
        }

        .user-btn {
            padding: 8px 15px;
            background: linear-gradient(145deg, #2a2a3e, #1a1a2e);
            border: 2px solid #ffd700;
            color: #ffd700;
            font-family: 'Orbitron', monospace;
            font-weight: bold;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .user-btn:hover {
            background: linear-gradient(145deg, #3a3a4e, #2a2a3e);
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.4);
            transform: translateY(-1px);
        }

        @media (max-width: 768px) {
            .modal-content {
                padding: 20px;
                margin: 10px;
            }

            .balance-actions {
                flex-direction: column;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .user-info {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- User Authentication Modal -->
    <div id="auth-modal" class="modal">
        <div class="modal-content">
            <div class="auth-container">
                <h2>🎰 Welcome to Vegas Ace Slots 🎰</h2>

                <!-- Login Form -->
                <div id="login-form" class="auth-form">
                    <h3>Login to Your Account</h3>
                    <div class="input-group">
                        <label>📱 Mobile Number:</label>
                        <input type="tel" id="login-mobile" placeholder="Enter your mobile number" maxlength="15">
                    </div>
                    <div class="input-group">
                        <label>🔒 Password:</label>
                        <input type="password" id="login-password" placeholder="Enter your password">
                    </div>
                    <button id="login-btn" class="auth-btn">🎯 LOGIN</button>
                    <p class="auth-switch">Don't have an account? <span id="show-register">Register here</span></p>
                </div>

                <!-- Register Form -->
                <div id="register-form" class="auth-form" style="display: none;">
                    <h3>Create New Account</h3>
                    <div class="input-group">
                        <label>👤 Full Name:</label>
                        <input type="text" id="register-name" placeholder="Enter your full name" maxlength="50">
                    </div>
                    <div class="input-group">
                        <label>📱 Mobile Number:</label>
                        <input type="tel" id="register-mobile" placeholder="Enter your mobile number" maxlength="15">
                    </div>
                    <div class="input-group">
                        <label>🔒 Password:</label>
                        <input type="password" id="register-password" placeholder="Create a password" minlength="6">
                    </div>
                    <div class="input-group">
                        <label>🔒 Confirm Password:</label>
                        <input type="password" id="register-confirm" placeholder="Confirm your password">
                    </div>
                    <button id="register-btn" class="auth-btn">🎊 CREATE ACCOUNT</button>
                    <p class="auth-switch">Already have an account? <span id="show-login">Login here</span></p>
                </div>
            </div>
        </div>
    </div>

    <!-- User Profile Modal -->
    <div id="profile-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="profile-container">
                <div class="profile-header">
                    <h2>👤 User Profile</h2>
                    <button id="close-profile" class="close-btn">✖</button>
                </div>

                <div class="profile-info">
                    <div class="user-details">
                        <h3>Account Information</h3>
                        <p><strong>Name:</strong> <span id="profile-name">-</span></p>
                        <p><strong>Mobile:</strong> <span id="profile-mobile">-</span></p>
                        <p><strong>Account ID:</strong> <span id="profile-id">-</span></p>
                        <p><strong>Member Since:</strong> <span id="profile-joined">-</span></p>
                    </div>

                    <div class="balance-section">
                        <h3>💰 Account Balance</h3>
                        <div class="balance-display-large">
                            $<span id="profile-balance">0.00</span>
                        </div>

                        <div class="balance-actions">
                            <button id="deposit-btn" class="action-btn deposit">💳 DEPOSIT</button>
                            <button id="withdraw-btn" class="action-btn withdraw">💸 WITHDRAW</button>
                        </div>
                    </div>
                </div>

                <div class="activity-section">
                    <h3>📊 Account Activity</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">Total Deposits:</span>
                            <span class="stat-value">$<span id="total-deposits">0.00</span></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Total Withdrawals:</span>
                            <span class="stat-value">$<span id="total-withdrawals">0.00</span></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Total Wagered:</span>
                            <span class="stat-value">$<span id="total-wagered">0.00</span></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Total Won:</span>
                            <span class="stat-value">$<span id="total-won">0.00</span></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Games Played:</span>
                            <span class="stat-value"><span id="games-played">0</span></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Win Rate:</span>
                            <span class="stat-value"><span id="win-rate">0%</span></span>
                        </div>
                    </div>
                </div>

                <div class="transaction-history">
                    <h3>📋 Recent Transactions</h3>
                    <div id="transaction-list" class="transaction-list">
                        <p class="no-transactions">No transactions yet</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Deposit Modal -->
    <div id="deposit-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="transaction-container">
                <h2>💳 Deposit Funds</h2>
                <div class="input-group">
                    <label>💰 Amount to Deposit:</label>
                    <input type="number" id="deposit-amount" placeholder="Enter amount" min="10" max="10000" step="0.01">
                </div>
                <div class="payment-methods">
                    <h3>Payment Method:</h3>
                    <div class="payment-options">
                        <label class="payment-option">
                            <input type="radio" name="deposit-method" value="card" checked>
                            <span>💳 Credit/Debit Card</span>
                        </label>
                        <label class="payment-option">
                            <input type="radio" name="deposit-method" value="bank">
                            <span>🏦 Bank Transfer</span>
                        </label>
                        <label class="payment-option">
                            <input type="radio" name="deposit-method" value="wallet">
                            <span>📱 Digital Wallet</span>
                        </label>
                    </div>
                </div>
                <div class="transaction-actions">
                    <button id="confirm-deposit" class="action-btn deposit">✅ CONFIRM DEPOSIT</button>
                    <button id="cancel-deposit" class="action-btn cancel">❌ CANCEL</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Withdraw Modal -->
    <div id="withdraw-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="transaction-container">
                <h2>💸 Withdraw Funds</h2>
                <div class="current-balance">
                    <p>Available Balance: $<span id="withdraw-available">0.00</span></p>
                </div>
                <div class="input-group">
                    <label>💰 Amount to Withdraw:</label>
                    <input type="number" id="withdraw-amount" placeholder="Enter amount" min="10" step="0.01">
                </div>
                <div class="withdrawal-info">
                    <h3>Withdrawal Information:</h3>
                    <div class="input-group">
                        <label>🏦 Bank Account Number:</label>
                        <input type="text" id="bank-account" placeholder="Enter bank account number">
                    </div>
                    <div class="input-group">
                        <label>🏛️ Bank Name:</label>
                        <input type="text" id="bank-name" placeholder="Enter bank name">
                    </div>
                </div>
                <div class="transaction-actions">
                    <button id="confirm-withdraw" class="action-btn withdraw">✅ CONFIRM WITHDRAWAL</button>
                    <button id="cancel-withdraw" class="action-btn cancel">❌ CANCEL</button>
                </div>
            </div>
        </div>
    </div>

    <div class="game-container" id="game-container" style="display: none;">
        <div class="header">
            <h1 class="title">VEGAS ACE SLOTS</h1>
            <div class="user-info">
                <div class="stats">
                    <div class="stat">
                        <div class="stat-label">BALANCE</div>
                        <div class="stat-value" id="balance">$0.00</div>
                    </div>
                    <div class="stat">
                        <div class="stat-label">BET</div>
                        <div class="stat-value" id="current-bet">$1.00</div>
                    </div>
                    <div class="stat">
                        <div class="stat-label">LAST WIN</div>
                        <div class="stat-value" id="last-win">$0.00</div>
                    </div>
                    <div class="stat multiplier-stat" id="header-multiplier">
                        <div class="stat-label">MULTIPLIER</div>
                        <div class="stat-value multiplier-value" id="header-multiplier-value">1X</div>
                    </div>
                </div>
                <div class="user-controls">
                    <button id="profile-btn" class="user-btn">👤 PROFILE</button>
                    <button id="logout-btn" class="user-btn">🚪 LOGOUT</button>
                </div>
            </div>
        </div>

        <div class="game-area">
            <div class="slot-machine">
                <div class="reels" id="reels">
                    <!-- Symbols will be generated here -->
                </div>
            </div>
        </div>

        <div class="controls">
            <div class="bet-section">
                <button class="btn" id="bet-down">BET -</button>
                <div class="bet-display">
                    <div class="bet-label">CURRENT BET</div>
                    <div class="bet-value" id="bet-display">$1.00</div>
                </div>
                <button class="btn" id="bet-up">BET +</button>
            </div>

            <button class="btn spin-btn" id="spin-button">
                <span id="spin-text">SPIN</span>
            </button>

            <button class="btn" id="max-bet">MAX BET</button>

            <button class="btn" id="music-toggle">🎼 MUSIC ON</button>
        </div>

        <!-- Bonus Displays -->
        <div class="bonus-displays">
            <div class="multiplier-display hidden" id="multiplier-display">
                <div class="bonus-text">MULTIPLIER</div>
                <div class="bonus-value" id="multiplier-value">x1</div>
            </div>
            <div class="freespins-display hidden" id="freespins-display">
                <div class="bonus-text">FREE SPINS</div>
                <div class="bonus-value" id="freespins-value">0</div>
            </div>
        </div>

        <!-- Win Popup -->
        <div class="win-popup hidden" id="win-popup">
            <div class="win-amount" id="win-amount-display">$0.00</div>
            <div class="win-type" id="win-type-display">WIN!</div>
        </div>

        <!-- Message Display -->
        <div class="message hidden" id="message-display"></div>
    </div>

    <!-- Simple HTML5 Audio System -->
    <audio id="bgMusic" loop>
        <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
    </audio>
    <audio id="spinSound">
        <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
    </audio>
    <audio id="winSound">
        <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
    </audio>
    <script>
        // 🔐 USER AUTHENTICATION & MANAGEMENT SYSTEM 🔐
        // ✅ Complete user registration, login, profile, and transaction system

        // User Management System
        const userSystem = {
            currentUser: null,
            isLoggedIn: false,

            // Initialize the authentication system
            init() {
                console.log('🔐 Initializing User Authentication System...');
                this.setupEventListeners();
                this.checkExistingSession();
            },

            // Setup all authentication event listeners
            setupEventListeners() {
                // Authentication form toggles
                document.getElementById('show-register').addEventListener('click', () => {
                    this.showRegisterForm();
                });

                document.getElementById('show-login').addEventListener('click', () => {
                    this.showLoginForm();
                });

                // Authentication buttons
                document.getElementById('login-btn').addEventListener('click', () => {
                    this.handleLogin();
                });

                document.getElementById('register-btn').addEventListener('click', () => {
                    this.handleRegister();
                });

                // Profile and logout buttons
                document.getElementById('profile-btn').addEventListener('click', () => {
                    this.showProfile();
                });

                document.getElementById('logout-btn').addEventListener('click', () => {
                    this.handleLogout();
                });

                document.getElementById('close-profile').addEventListener('click', () => {
                    this.hideProfile();
                });

                // Transaction buttons
                document.getElementById('deposit-btn').addEventListener('click', () => {
                    this.showDepositModal();
                });

                document.getElementById('withdraw-btn').addEventListener('click', () => {
                    this.showWithdrawModal();
                });

                document.getElementById('confirm-deposit').addEventListener('click', () => {
                    this.processDeposit();
                });

                document.getElementById('confirm-withdraw').addEventListener('click', () => {
                    this.processWithdrawal();
                });

                document.getElementById('cancel-deposit').addEventListener('click', () => {
                    this.hideDepositModal();
                });

                document.getElementById('cancel-withdraw').addEventListener('click', () => {
                    this.hideWithdrawModal();
                });

                // Enter key support for forms
                document.getElementById('login-password').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') this.handleLogin();
                });

                document.getElementById('register-confirm').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') this.handleRegister();
                });
            },

            // Check if user is already logged in
            checkExistingSession() {
                const savedUser = localStorage.getItem('vegasAceCurrentUser');
                if (savedUser) {
                    this.currentUser = JSON.parse(savedUser);
                    this.isLoggedIn = true;
                    this.showGameInterface();
                    console.log('✅ User session restored:', this.currentUser.name);
                } else {
                    this.showAuthInterface();
                }
            },

            // Show/Hide interface functions
            showAuthInterface() {
                document.getElementById('auth-modal').style.display = 'flex';
                document.getElementById('game-container').style.display = 'none';
            },

            showGameInterface() {
                document.getElementById('auth-modal').style.display = 'none';
                document.getElementById('game-container').style.display = 'block';
                this.updateUserDisplay();
                // Initialize game after showing interface
                if (typeof initGame === 'function') {
                    initGame();
                }
            },

            showRegisterForm() {
                document.getElementById('login-form').style.display = 'none';
                document.getElementById('register-form').style.display = 'block';
            },

            showLoginForm() {
                document.getElementById('register-form').style.display = 'none';
                document.getElementById('login-form').style.display = 'block';
            },

            // User registration
            handleRegister() {
                const name = document.getElementById('register-name').value.trim();
                const mobile = document.getElementById('register-mobile').value.trim();
                const password = document.getElementById('register-password').value;
                const confirmPassword = document.getElementById('register-confirm').value;

                // Validation
                if (!name || !mobile || !password || !confirmPassword) {
                    this.showMessage('❌ Please fill in all fields!', 'error');
                    return;
                }

                if (password.length < 6) {
                    this.showMessage('❌ Password must be at least 6 characters!', 'error');
                    return;
                }

                if (password !== confirmPassword) {
                    this.showMessage('❌ Passwords do not match!', 'error');
                    return;
                }

                if (!this.validateMobile(mobile)) {
                    this.showMessage('❌ Please enter a valid mobile number!', 'error');
                    return;
                }

                // Check if user already exists
                if (this.userExists(mobile)) {
                    this.showMessage('❌ User with this mobile number already exists!', 'error');
                    return;
                }

                // Create new user
                const newUser = this.createUser(name, mobile, password);
                this.saveUser(newUser);
                this.currentUser = newUser;
                this.isLoggedIn = true;

                this.showMessage('🎊 Account created successfully! Welcome to Vegas Ace Slots!', 'success');
                setTimeout(() => {
                    this.showGameInterface();
                }, 2000);

                console.log('✅ New user registered:', newUser.name);
            },

            // User login
            handleLogin() {
                const mobile = document.getElementById('login-mobile').value.trim();
                const password = document.getElementById('login-password').value;

                if (!mobile || !password) {
                    this.showMessage('❌ Please enter mobile number and password!', 'error');
                    return;
                }

                const user = this.authenticateUser(mobile, password);
                if (user) {
                    this.currentUser = user;
                    this.isLoggedIn = true;
                    localStorage.setItem('vegasAceCurrentUser', JSON.stringify(user));

                    this.showMessage('🎯 Login successful! Welcome back!', 'success');
                    setTimeout(() => {
                        this.showGameInterface();
                    }, 1500);

                    console.log('✅ User logged in:', user.name);
                } else {
                    this.showMessage('❌ Invalid mobile number or password!', 'error');
                }
            },

            // User logout
            handleLogout() {
                if (confirm('Are you sure you want to logout?')) {
                    // Save current game state to user profile
                    if (this.currentUser && typeof gameState !== 'undefined') {
                        this.currentUser.balance = gameState.balance;
                        this.saveUser(this.currentUser);
                    }

                    this.currentUser = null;
                    this.isLoggedIn = false;
                    localStorage.removeItem('vegasAceCurrentUser');

                    this.showMessage('👋 Logged out successfully!', 'info');
                    setTimeout(() => {
                        this.showAuthInterface();
                        this.clearForms();
                    }, 1500);

                    console.log('✅ User logged out');
                }
            },

            // Create new user object
            createUser(name, mobile, password) {
                const userId = 'VA' + Date.now().toString().slice(-8);
                return {
                    id: userId,
                    name: name,
                    mobile: mobile,
                    password: this.hashPassword(password),
                    balance: 1000.00, // Starting balance
                    joinDate: new Date().toISOString(),
                    stats: {
                        totalDeposits: 1000.00, // Starting deposit
                        totalWithdrawals: 0.00,
                        totalWagered: 0.00,
                        totalWon: 0.00,
                        gamesPlayed: 0,
                        winRate: 0
                    },
                    transactions: [
                        {
                            id: 'TXN' + Date.now(),
                            type: 'deposit',
                            amount: 1000.00,
                            method: 'Welcome Bonus',
                            timestamp: new Date().toISOString(),
                            status: 'completed'
                        }
                    ]
                };
            },

            // Simple password hashing (in production, use proper hashing)
            hashPassword(password) {
                let hash = 0;
                for (let i = 0; i < password.length; i++) {
                    const char = password.charCodeAt(i);
                    hash = ((hash << 5) - hash) + char;
                    hash = hash & hash; // Convert to 32-bit integer
                }
                return hash.toString();
            },

            // Validate mobile number format
            validateMobile(mobile) {
                const mobileRegex = /^[+]?[\d\s\-\(\)]{10,15}$/;
                return mobileRegex.test(mobile);
            },

            // Check if user exists
            userExists(mobile) {
                const users = this.getAllUsers();
                return users.some(user => user.mobile === mobile);
            },

            // Authenticate user
            authenticateUser(mobile, password) {
                const users = this.getAllUsers();
                const hashedPassword = this.hashPassword(password);
                return users.find(user => user.mobile === mobile && user.password === hashedPassword);
            },

            // Save user to localStorage
            saveUser(user) {
                const users = this.getAllUsers();
                const existingIndex = users.findIndex(u => u.id === user.id);

                if (existingIndex >= 0) {
                    users[existingIndex] = user;
                } else {
                    users.push(user);
                }

                localStorage.setItem('vegasAceUsers', JSON.stringify(users));

                // Update current session
                if (this.currentUser && this.currentUser.id === user.id) {
                    this.currentUser = user;
                    localStorage.setItem('vegasAceCurrentUser', JSON.stringify(user));
                }
            },

            // Get all users from localStorage
            getAllUsers() {
                const users = localStorage.getItem('vegasAceUsers');
                return users ? JSON.parse(users) : [];
            },

            // Update user display in game interface
            updateUserDisplay() {
                if (!this.currentUser) return;

                // Update balance display
                document.getElementById('balance').textContent = this.currentUser.balance.toFixed(2);

                // Update game state balance
                if (typeof gameState !== 'undefined') {
                    gameState.balance = this.currentUser.balance;
                }
            },

            // Show profile modal
            showProfile() {
                if (!this.currentUser) return;

                // Update profile information
                document.getElementById('profile-name').textContent = this.currentUser.name;
                document.getElementById('profile-mobile').textContent = this.currentUser.mobile;
                document.getElementById('profile-id').textContent = this.currentUser.id;
                document.getElementById('profile-joined').textContent = new Date(this.currentUser.joinDate).toLocaleDateString();
                document.getElementById('profile-balance').textContent = this.currentUser.balance.toFixed(2);

                // Update statistics
                document.getElementById('total-deposits').textContent = this.currentUser.stats.totalDeposits.toFixed(2);
                document.getElementById('total-withdrawals').textContent = this.currentUser.stats.totalWithdrawals.toFixed(2);
                document.getElementById('total-wagered').textContent = this.currentUser.stats.totalWagered.toFixed(2);
                document.getElementById('total-won').textContent = this.currentUser.stats.totalWon.toFixed(2);
                document.getElementById('games-played').textContent = this.currentUser.stats.gamesPlayed;
                document.getElementById('win-rate').textContent = this.currentUser.stats.winRate.toFixed(1) + '%';

                // Update transaction history
                this.updateTransactionHistory();

                document.getElementById('profile-modal').style.display = 'flex';
            },

            hideProfile() {
                document.getElementById('profile-modal').style.display = 'none';
            },

            // Show deposit modal
            showDepositModal() {
                document.getElementById('deposit-modal').style.display = 'flex';
                document.getElementById('deposit-amount').value = '';
            },

            hideDepositModal() {
                document.getElementById('deposit-modal').style.display = 'none';
            },

            // Show withdraw modal
            showWithdrawModal() {
                document.getElementById('withdraw-available').textContent = this.currentUser.balance.toFixed(2);
                document.getElementById('withdraw-modal').style.display = 'flex';
                document.getElementById('withdraw-amount').value = '';
                document.getElementById('withdraw-amount').max = this.currentUser.balance;
            },

            hideWithdrawModal() {
                document.getElementById('withdraw-modal').style.display = 'none';
            },

            // Process deposit
            processDeposit() {
                const amount = parseFloat(document.getElementById('deposit-amount').value);
                const method = document.querySelector('input[name="deposit-method"]:checked').value;

                if (!amount || amount < 10) {
                    this.showMessage('❌ Minimum deposit amount is $10!', 'error');
                    return;
                }

                if (amount > 10000) {
                    this.showMessage('❌ Maximum deposit amount is $10,000!', 'error');
                    return;
                }

                // Process the deposit
                this.currentUser.balance += amount;
                this.currentUser.stats.totalDeposits += amount;

                // Add transaction record
                const transaction = {
                    id: 'TXN' + Date.now(),
                    type: 'deposit',
                    amount: amount,
                    method: method,
                    timestamp: new Date().toISOString(),
                    status: 'completed'
                };
                this.currentUser.transactions.unshift(transaction);

                // Save user data
                this.saveUser(this.currentUser);
                this.updateUserDisplay();

                this.showMessage(`💳 Successfully deposited $${amount.toFixed(2)}!`, 'success');
                this.hideDepositModal();

                console.log('✅ Deposit processed:', amount);
            },

            // Process withdrawal
            processWithdrawal() {
                const amount = parseFloat(document.getElementById('withdraw-amount').value);
                const bankAccount = document.getElementById('bank-account').value.trim();
                const bankName = document.getElementById('bank-name').value.trim();

                if (!amount || amount < 10) {
                    this.showMessage('❌ Minimum withdrawal amount is $10!', 'error');
                    return;
                }

                if (amount > this.currentUser.balance) {
                    this.showMessage('❌ Insufficient balance!', 'error');
                    return;
                }

                if (!bankAccount || !bankName) {
                    this.showMessage('❌ Please provide bank details!', 'error');
                    return;
                }

                // Process the withdrawal
                this.currentUser.balance -= amount;
                this.currentUser.stats.totalWithdrawals += amount;

                // Add transaction record
                const transaction = {
                    id: 'TXN' + Date.now(),
                    type: 'withdraw',
                    amount: amount,
                    method: `${bankName} - ${bankAccount}`,
                    timestamp: new Date().toISOString(),
                    status: 'completed'
                };
                this.currentUser.transactions.unshift(transaction);

                // Save user data
                this.saveUser(this.currentUser);
                this.updateUserDisplay();

                this.showMessage(`💸 Successfully withdrew $${amount.toFixed(2)}!`, 'success');
                this.hideWithdrawModal();

                console.log('✅ Withdrawal processed:', amount);
            },

            // Update transaction history display
            updateTransactionHistory() {
                const transactionList = document.getElementById('transaction-list');
                const transactions = this.currentUser.transactions.slice(0, 10); // Show last 10 transactions

                if (transactions.length === 0) {
                    transactionList.innerHTML = '<p class="no-transactions">No transactions yet</p>';
                    return;
                }

                transactionList.innerHTML = transactions.map(transaction => {
                    const date = new Date(transaction.timestamp).toLocaleDateString();
                    const time = new Date(transaction.timestamp).toLocaleTimeString();
                    return `
                        <div class="transaction-item">
                            <div>
                                <span class="transaction-type ${transaction.type}">${transaction.type.toUpperCase()}</span>
                                <br>
                                <small>${date} ${time}</small>
                            </div>
                            <div>
                                <strong>$${transaction.amount.toFixed(2)}</strong>
                                <br>
                                <small>${transaction.method}</small>
                            </div>
                        </div>
                    `;
                }).join('');
            },

            // Track game activity
            trackGameActivity(type, amount = 0) {
                if (!this.currentUser) return;

                switch (type) {
                    case 'bet':
                        this.currentUser.stats.totalWagered += amount;
                        this.currentUser.stats.gamesPlayed++;
                        break;
                    case 'win':
                        this.currentUser.stats.totalWon += amount;
                        break;
                }

                // Calculate win rate
                if (this.currentUser.stats.gamesPlayed > 0) {
                    const winGames = this.currentUser.transactions.filter(t => t.type === 'win').length;
                    this.currentUser.stats.winRate = (winGames / this.currentUser.stats.gamesPlayed) * 100;
                }

                // Add transaction for significant wins
                if (type === 'win' && amount > 0) {
                    const transaction = {
                        id: 'TXN' + Date.now(),
                        type: 'win',
                        amount: amount,
                        method: 'Slot Game',
                        timestamp: new Date().toISOString(),
                        status: 'completed'
                    };
                    this.currentUser.transactions.unshift(transaction);
                }

                this.saveUser(this.currentUser);
            },

            // Show message to user
            showMessage(message, type = 'info') {
                // Create or update message element
                let messageEl = document.getElementById('auth-message');
                if (!messageEl) {
                    messageEl = document.createElement('div');
                    messageEl.id = 'auth-message';
                    messageEl.style.cssText = `
                        position: fixed;
                        top: 20px;
                        left: 50%;
                        transform: translateX(-50%);
                        padding: 15px 25px;
                        border-radius: 10px;
                        font-weight: bold;
                        z-index: 3000;
                        font-family: 'Orbitron', monospace;
                    `;
                    document.body.appendChild(messageEl);
                }

                // Set message style based on type
                const styles = {
                    success: 'background: #28a745; color: white; border: 2px solid #20c997;',
                    error: 'background: #dc3545; color: white; border: 2px solid #fd7e14;',
                    info: 'background: #17a2b8; color: white; border: 2px solid #20c997;'
                };

                messageEl.style.cssText += styles[type] || styles.info;
                messageEl.textContent = message;
                messageEl.style.display = 'block';

                // Auto-hide after 3 seconds
                setTimeout(() => {
                    if (messageEl) {
                        messageEl.style.display = 'none';
                    }
                }, 3000);
            },

            // Clear form inputs
            clearForms() {
                document.getElementById('login-mobile').value = '';
                document.getElementById('login-password').value = '';
                document.getElementById('register-name').value = '';
                document.getElementById('register-mobile').value = '';
                document.getElementById('register-password').value = '';
                document.getElementById('register-confirm').value = '';
            }
        };

        // Initialize user system when page loads
        document.addEventListener('DOMContentLoaded', () => {
            userSystem.init();
        });

        // Game State
        const gameState = {
            balance: 1000.00,
            currentBet: 1.00,
            isSpinning: false,
            inFreeSpins: false,
            freeSpinsRemaining: 0,
            currentMultiplier: 1,
            lastWin: 0,
            consecutiveWins: 0,
            isCoolingSpin: false
        };

        // Advanced Casino Configuration - Modern Slot Logic
        const config = {
            reels: 5,
            rows: 4,
            symbols: ['9', '10', 'J', 'Q', 'K', 'A', '⭐', '🃏'],
            betLevels: [0.10, 0.25, 0.50, 1.00, 2.00, 5.00, 10.00, 25.00, 50.00, 100.00],

            // Multiple Virtual Reel Sets - Optimized for frequent small wins and LDW
            reelSets: {
                normal: [32, 30, 32, 32, 28, 22, 1.5, 5],      // More J,Q for frequent small wins
                generous: [28, 26, 30, 30, 32, 28, 2.5, 8],    // Even more medium symbols
                retention: [22, 22, 28, 25, 38, 35, 4.0, 12],  // Maximum small wins
                bonus: [38, 33, 28, 23, 18, 13, 0.3, 4],       // Suppress bonuses temporarily
                freespins: [20, 18, 25, 22, 45, 40, 0, 15]     // Free spins mode - more premium symbols
            },

            // Balanced payouts - Natural casino feel (like Super Ace)
            payouts: {
                '9': [0, 0, 0.2, 0.8, 2.0],     // Low but fair
                '10': [0, 0, 0.3, 1.0, 2.5],    // Slightly better
                'J': [0, 0, 0.4, 1.5, 4.0],     // Medium-low
                'Q': [0, 0, 0.6, 2.0, 6.0],     // Medium
                'K': [0, 0, 0.8, 3.0, 8.0],     // Good
                'A': [0, 0, 1.2, 5.0, 15.0],    // Premium
                '⭐': [0, 0, 0, 0, 0],           // Scatter - no line pays
                '🃏': [0, 0, 2.0, 8.0, 25.0]    // Wild - best symbol
            },

            // Psychological color scheme
            colors: {
                '9': '#8B4513', '10': '#CD853F', 'J': '#4169E1', 'Q': '#9932CC',
                'K': '#DC143C', 'A': '#FFD700', '⭐': '#FF1493', '🃏': '#00FF00'
            },

            // Advanced Casino Mechanics
            targetRTP: 0.85, // 85% RTP target
            adaptiveRange: 0.05, // ±5% RTP adjustment range
            nearMissChance: 0.25, // 25% chance for near-miss psychology
            maxMultiplier: 5, // Limited to 5x

            // Adaptive Bonus Control - More frequent small celebrations
            maxSpinsWithoutBonus: 120, // Force bonus after 120 spins
            minSpinsBetweenBigWins: 15, // Prevent clustering
            ldwCelebrationThreshold: 0.15, // Celebrate wins > 15% of bet (more frequent)

            // Free Spins Control - Enhanced with auto-play and guaranteed returns
            freeSpinsBase: 10,
            freeSpinsRetrigger: 5,
            maxRetriggers: 1,
            freeSpinsMinReturn: 8.0, // Minimum 8x bet return
            freeSpinsMaxReturn: 40.0, // Maximum 40x bet return
            freeSpinsMinBalanceReturn: 0.30, // Minimum 30% of starting balance return
            autoPlaySpeed: 800, // Auto-play speed in milliseconds
            specialEffectDuration: 2000 // Special effects duration
        };

        let currentBetIndex = 3; // Start at $1.00
        let symbolGrid = [];

        // Advanced Session Tracking for Adaptive RTP
        const sessionData = {
            totalSpins: 0,
            totalWagered: 0,
            totalWon: 0,
            netLoss: 0,
            consecutiveLosses: 0,
            lastBigWin: 0,
            spinsWithoutScatter: 0,
            freeSpinsRetriggered: 0,
            sessionStartBalance: 1000,
            nearMissCount: 0,
            smallWinStreak: 0,
            spinsWithoutWin: 0,
            lastScatterSpin: 0,
            freeSpinsWinTotal: 0,

            // Advanced Tracking
            lastBigWinSpin: -999,
            currentSessionRTP: 0,
            recentWinHistory: [], // Last 20 spins win/loss
            ldwCount: 0, // Loss-Disguised-as-Win count
            nearMissStreak: 0,
            bonusSuppressionActive: false,
            currentReelSet: 'normal',
            adaptiveAdjustments: 0,

            // Enhanced Free Spins Tracking
            isAutoPlaying: false,
            autoPlayRemaining: 0,
            freeSpinsStartBalance: 0,
            guaranteedReturn: 0,
            currentFreeSpinWins: 0
        };

        // Player financial analysis for dynamic adjustments
        function getPlayerFinancialState() {
            const balancePercentage = gameState.balance / sessionData.sessionStartBalance;
            const netLossPercentage = sessionData.netLoss / sessionData.sessionStartBalance;

            return {
                balancePercentage: balancePercentage,
                netLossPercentage: netLossPercentage,
                isLowBalance: balancePercentage < 0.3, // Less than 30% of starting balance
                isCriticalBalance: balancePercentage < 0.15, // Less than 15% of starting balance
                hasLostSignificant: netLossPercentage > 0.4, // Lost more than 40% of starting balance
                needsWin: sessionData.spinsWithoutWin > 8 || sessionData.consecutiveLosses > 5, // More frequent small wins
                needsScatter: sessionData.spinsWithoutScatter > 60 && balancePercentage < 0.5
            };
        }

        // Advanced Adaptive RTP System
        function calculateAdaptiveRTP() {
            if (sessionData.totalSpins < 10) return config.targetRTP;

            sessionData.currentSessionRTP = sessionData.totalWon / sessionData.totalWagered;
            const rtpDeviation = sessionData.currentSessionRTP - config.targetRTP;

            // If player is winning too much, slightly reduce RTP
            // If player is losing too much, slightly increase RTP
            let adaptiveRTP = config.targetRTP;

            if (Math.abs(rtpDeviation) > config.adaptiveRange) {
                if (rtpDeviation > 0) {
                    // Player winning too much - reduce RTP slightly
                    adaptiveRTP = config.targetRTP - (config.adaptiveRange * 0.5);
                    sessionData.bonusSuppressionActive = true;
                } else {
                    // Player losing too much - increase RTP slightly
                    adaptiveRTP = config.targetRTP + (config.adaptiveRange * 0.5);
                    sessionData.bonusSuppressionActive = false;
                }
                sessionData.adaptiveAdjustments++;
            }

            return adaptiveRTP;
        }

        // Dynamic Reel Set Selection - Favor small wins more often
        function selectOptimalReelSet() {
            const playerState = getPlayerFinancialState();
            const currentRTP = calculateAdaptiveRTP();

            // Special reel set for free spins - use premium symbols for big wins
            if (gameState.inFreeSpins) {
                sessionData.currentReelSet = 'freespins'; // Premium symbols for free spins
                console.log(`Free spins mode: Using premium reel set for big wins with 5x multipliers`);
                return config.reelSets[sessionData.currentReelSet];
            }

            // Choose reel set based on player state and RTP needs
            if (sessionData.bonusSuppressionActive) {
                sessionData.currentReelSet = 'bonus'; // Suppress bonuses
            } else if (playerState.isCriticalBalance || currentRTP < config.targetRTP) {
                sessionData.currentReelSet = 'retention'; // Player retention mode
            } else if (playerState.needsWin || sessionData.consecutiveLosses > 5) { // Reduced from 8 to 5
                sessionData.currentReelSet = 'generous'; // More generous
            } else if (sessionData.spinsWithoutWin > 8) { // More frequent generous mode
                sessionData.currentReelSet = 'generous'; // Give small wins more often
            } else {
                sessionData.currentReelSet = 'normal'; // Standard play
            }

            console.log(`Selected reel set: ${sessionData.currentReelSet}, RTP: ${currentRTP.toFixed(3)}`);
            return config.reelSets[sessionData.currentReelSet];
        }

        // Calculate guaranteed return for free spins - minimum 30% of starting balance
        function calculateGuaranteedFreeSpinsReturn() {
            const playerState = getPlayerFinancialState();
            const baseBet = gameState.currentBet;

            // Calculate minimum return based on starting balance (30%)
            const balanceBasedReturn = sessionData.sessionStartBalance * config.freeSpinsMinBalanceReturn;

            // Calculate bet-based return
            let betBasedMultiplier;
            if (playerState.isCriticalBalance) {
                betBasedMultiplier = config.freeSpinsMaxReturn * 0.9; // 90% of max
            } else if (playerState.isLowBalance) {
                betBasedMultiplier = config.freeSpinsMaxReturn * 0.7; // 70% of max
            } else if (playerState.hasLostSignificant) {
                betBasedMultiplier = config.freeSpinsMaxReturn * 0.5; // 50% of max
            } else {
                betBasedMultiplier = config.freeSpinsMinReturn + Math.random() *
                    (config.freeSpinsMaxReturn * 0.4); // Min to 40% of max
            }

            const betBasedReturn = baseBet * betBasedMultiplier;

            // Use the higher of the two calculations
            const guaranteedReturn = Math.max(balanceBasedReturn, betBasedReturn);

            console.log(`Free spins guaranteed return: $${guaranteedReturn.toFixed(2)} (Balance: $${balanceBasedReturn.toFixed(2)}, Bet: $${betBasedReturn.toFixed(2)})`);

            return guaranteedReturn;
        }

        // 🎵 SIMPLE HTML5 AUDIO SYSTEM 🎵
        // ✅ Guaranteed to work in all browsers
        const audioSystem = {
            // Get audio elements
            bgMusic: document.getElementById('bgMusic'),
            spinSound: document.getElementById('spinSound'),
            winSound: document.getElementById('winSound'),
            musicEnabled: true,
            isInitialized: false,

            // Simple initialization
            async init() {
                console.log('🎵 Initializing Simple Audio System...');
                this.isInitialized = true;

                // Set volumes
                if (this.bgMusic) this.bgMusic.volume = 0.3;
                if (this.spinSound) this.spinSound.volume = 0.7;
                if (this.winSound) this.winSound.volume = 0.8;

                console.log('✅ Simple Audio System Ready!');
            },

            // Play background music
            startBackgroundMusic() {
                if (!this.musicEnabled || !this.bgMusic) return;
                try {
                    this.bgMusic.currentTime = 0;
                    this.bgMusic.play().then(() => {
                        console.log('🎼 ✅ Background music playing!');
                    }).catch(e => {
                        console.log('🎼 ❌ Background music failed:', e);
                    });
                } catch (e) {
                    console.log('🎼 ❌ Background music error:', e);
                }
            },

            // Stop background music
            stopBackgroundMusic() {
                if (this.bgMusic) {
                    this.bgMusic.pause();
                    this.bgMusic.currentTime = 0;
                    console.log('🎼 Background music stopped');
                }
            },

            // Play spin sound
            playSpinButtonSound() {
                if (!this.musicEnabled || !this.spinSound) return;
                try {
                    this.spinSound.currentTime = 0;
                    this.spinSound.play().then(() => {
                        console.log('🎛️ ✅ Spin sound playing!');
                    }).catch(e => {
                        console.log('🎛️ ❌ Spin sound failed:', e);
                    });
                } catch (e) {
                    console.log('🎛️ ❌ Spin sound error:', e);
                }
            },

            // Play win sound
            playWinSound(winAmount, betAmount, multiplier = 1) {
                if (!this.musicEnabled || !this.winSound) return;
                try {
                    this.winSound.currentTime = 0;
                    this.winSound.play().then(() => {
                        console.log('🎰 ✅ Win sound playing!');
                    }).catch(e => {
                        console.log('🎰 ❌ Win sound failed:', e);
                    });
                } catch (e) {
                    console.log('🎰 ❌ Win sound error:', e);
                }
            },

            // Toggle music
            toggleMusic() {
                this.musicEnabled = !this.musicEnabled;
                if (this.musicEnabled) {
                    this.startBackgroundMusic();
                    console.log('🎵 MUSIC ENABLED!');
                    } else {
                    this.stopBackgroundMusic();
                    console.log('🔇 MUSIC DISABLED!');
                }
                return this.musicEnabled;
            },

            // Dummy methods for compatibility
            playReelSpinSound() { this.playSpinButtonSound(); },
            stopReelSpinSound() { },
            playMultiplierSound() { this.playWinSound(); },
            playCheerSound() { this.playWinSound(); },
            playCoinDropSound() { this.playWinSound(); },
            playBonusSound() { this.playWinSound(); },
            playFreeSpinSound() { this.playWinSound(); },
            playScatterSound() { this.playWinSound(); },
            playCardFlipSound() { this.playSpinButtonSound(); },
            playCardMatchSound() { this.playWinSound(); },
            playCardComboSound() { this.playWinSound(); },
            playAnticipation() { this.playSpinButtonSound(); },
            playModernWin() { this.playWinSound(); }
        };

        // Initialize Game with Audio System
        async function initGame() {
            console.log('🎮 Initializing Vegas Ace Slots...');

            // Initialize audio system
            await audioSystem.init();

            // Sync game state with user balance
            if (userSystem.isLoggedIn && userSystem.currentUser) {
                gameState.balance = userSystem.currentUser.balance;
                sessionData.sessionStartBalance = userSystem.currentUser.balance;
            }

            createReels();
            populateReels();
            setupEventListeners();
            updateDisplay();

            console.log('✅ Game initialized successfully! Click SPIN to enable audio.');
        }

        // Add event listener to enable audio on first click
        document.addEventListener('click', function enableAudio() {
            if (audioSystem.musicEnabled && audioSystem.bgMusic) {
                audioSystem.startBackgroundMusic();
                console.log('🎵 Audio enabled on first click!');
            }
            // Remove this listener after first click
            document.removeEventListener('click', enableAudio);
        }, { once: true });

        function createReels() {
            const reelsContainer = document.getElementById('reels');
            reelsContainer.innerHTML = '';
            symbolGrid = [];

            for (let reel = 0; reel < config.reels; reel++) {
                symbolGrid[reel] = [];
                for (let row = 0; row < config.rows; row++) {
                    const symbolElement = document.createElement('div');
                    symbolElement.className = 'symbol';
                    symbolElement.id = `symbol-${reel}-${row}`;
                    reelsContainer.appendChild(symbolElement);
                    symbolGrid[reel][row] = null;
                }
            }
        }

        function populateReels() {
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    const symbol = getRandomSymbol();
                    symbolGrid[reel][row] = symbol;
                    updateSymbolDisplay(reel, row, symbol);
                }
            }
        }

        function getRandomSymbol(isScatterAllowed = true, forceNearMiss = false) {
            // Get optimal reel set based on current session state
            const symbolWeights = [...selectOptimalReelSet()];

            // Bonus suppression logic
            if (sessionData.bonusSuppressionActive && !forceNearMiss) {
                symbolWeights[6] *= 0.3; // Reduce scatter chance significantly
                console.log('Bonus suppression active - reducing scatter chance');
            }

            // Guaranteed bonus trigger after max spins
            if (sessionData.spinsWithoutScatter >= config.maxSpinsWithoutBonus && isScatterAllowed) {
                // Force scatter appearance
                symbolWeights[6] += 50;
                console.log('Forcing bonus trigger after max spins');
            }

            // Big win clustering prevention
            const spinsSinceLastBigWin = sessionData.totalSpins - sessionData.lastBigWinSpin;
            if (spinsSinceLastBigWin < config.minSpinsBetweenBigWins) {
                // Reduce high-value symbols temporarily
                symbolWeights[5] *= 0.7; // A
                symbolWeights[7] *= 0.5; // Wild
                console.log('Preventing big win clustering');
            }

            // Near-miss psychology for scatters
            if (forceNearMiss) {
                symbolWeights[6] = 0; // No scatter on near-miss
                symbolWeights[7] += 2; // More wilds for excitement
            }

            if (!isScatterAllowed) {
                symbolWeights[6] = 0; // Force no scatter
            }

            const totalWeight = symbolWeights.reduce((a, b) => a + b, 0);
            let random = Math.random() * totalWeight;

            for (let i = 0; i < config.symbols.length; i++) {
                random -= symbolWeights[i];
                if (random <= 0) {
                    return config.symbols[i];
                }
            }
            return config.symbols[0];
        }

        function updateSymbolDisplay(reel, row, symbol) {
            const element = document.getElementById(`symbol-${reel}-${row}`);
            if (element) {
                element.textContent = symbol;
                element.style.color = config.colors[symbol] || '#FFD700';
            }
        }

        function setupEventListeners() {
            document.getElementById('spin-button').addEventListener('click', () => {
                console.log('Spin button clicked');
                spin();
            });

            document.getElementById('bet-down').addEventListener('click', () => {
                console.log('Bet down clicked');
                decreaseBet();
            });

            document.getElementById('bet-up').addEventListener('click', () => {
                console.log('Bet up clicked');
                increaseBet();
            });

            document.getElementById('max-bet').addEventListener('click', () => {
                console.log('Max bet clicked');
                setMaxBet();
            });

            document.getElementById('music-toggle').addEventListener('click', () => {
                console.log('Music toggle clicked');
                toggleBackgroundMusic();
            });

            // Keyboard controls
            document.addEventListener('keydown', (event) => {
                if (event.code === 'Space') {
                    event.preventDefault();
                    spin();
                }
            });
        }

        // Main Spin Function with Casino Psychology
        async function spin() {
            console.log('Spin function called');

            if (gameState.isSpinning) {
                console.log('Already spinning, ignoring');
                return;
            }

            if (gameState.balance < gameState.currentBet && !gameState.inFreeSpins) {
                showMessage('Insufficient balance!');
                return;
            }

            gameState.isSpinning = true;
            gameState.lastWin = 0;
            gameState.currentMultiplier = 1; // Always start at 1x

            // 🎛️ Play satisfying spin button sound
            audioSystem.playSpinButtonSound();

            // 🎰 Start reel spinning sound for anticipation
            setTimeout(() => {
                audioSystem.playReelSpinSound();
            }, 200); // Slight delay after button click

            // Session tracking
            sessionData.totalSpins++;
            sessionData.spinsWithoutScatter++;
            sessionData.spinsWithoutWin++; // Track spins without any win

            // Deduct bet and track wagering
            if (!gameState.inFreeSpins) {
                gameState.balance -= gameState.currentBet;
                sessionData.totalWagered += gameState.currentBet;
                sessionData.netLoss = sessionData.totalWagered - sessionData.totalWon;

                // Track user activity
                if (userSystem.isLoggedIn) {
                    userSystem.currentUser.balance = gameState.balance;
                    userSystem.trackGameActivity('bet', gameState.currentBet);
                    userSystem.updateUserDisplay();
                }
            } else {
                gameState.freeSpinsRemaining--;
            }

            updateDisplay();

            // Animate spinning with actual symbol changes
            await animateSpinning();

            // Generate final result
            generateNewGrid();

            // Start cascade sequence
            await cascadeSequence();

            gameState.isSpinning = false;
            updateDisplay();

            // Check if free spins ended
            if (gameState.inFreeSpins && gameState.freeSpinsRemaining <= 0) {
                gameState.inFreeSpins = false;
                sessionData.isAutoPlaying = false;
                hideElement('freespins-display');

                // Ensure guaranteed return (minimum 30% of starting balance)
                const actualReturn = sessionData.freeSpinsWinTotal;
                const guaranteedReturn = sessionData.guaranteedReturn;

                console.log('Free spins ended - Actual won:', actualReturn, 'Guaranteed:', guaranteedReturn);

                // If actual return is less than guaranteed, add the difference
                if (actualReturn < guaranteedReturn) {
                    const bonusAmount = guaranteedReturn - actualReturn;
                    gameState.balance += bonusAmount;
                    sessionData.totalWon += bonusAmount;
                    sessionData.freeSpinsWinTotal += bonusAmount;

                    showMessage(`Free Spins Complete! Guaranteed Bonus: $${bonusAmount.toFixed(2)}`);
                    console.log('Added guaranteed return bonus:', bonusAmount);
                } else {
                    showMessage('Free Spins Complete! Great wins!');
                }

                // Log final free spins performance
                const totalReturn = sessionData.freeSpinsWinTotal;
                const returnPercentage = (totalReturn / sessionData.sessionStartBalance) * 100;
                console.log(`Free spins total return: $${totalReturn.toFixed(2)} (${returnPercentage.toFixed(1)}% of starting balance)`);

                // Reset free spins tracking
                sessionData.freeSpinsWinTotal = 0;
                sessionData.guaranteedReturn = 0;
                sessionData.currentFreeSpinWins = 0;
            }
        }

        async function animateSpinning() {
            const spinDuration = 2000; // 2 seconds
            const changeInterval = 100; // Change symbols every 100ms
            const totalChanges = spinDuration / changeInterval;

            // Start spinning animation for all symbols
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    const element = document.getElementById(`symbol-${reel}-${row}`);
                    if (element) {
                        element.classList.add('spinning');
                    }
                }
            }

            // Change symbols rapidly during spin
            for (let i = 0; i < totalChanges; i++) {
                for (let reel = 0; reel < config.reels; reel++) {
                    for (let row = 0; row < config.rows; row++) {
                        const randomSymbol = getRandomSymbol();
                        updateSymbolDisplay(reel, row, randomSymbol);
                    }
                }
                await new Promise(resolve => setTimeout(resolve, changeInterval));
            }

            // Remove spinning animation and stop reel sound
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    const element = document.getElementById(`symbol-${reel}-${row}`);
                    if (element) {
                        element.classList.remove('spinning');
                    }
                }
            }

            // 🎰 Stop reel spinning sound - anticipation complete
            audioSystem.stopReelSpinSound();
        }

        function generateNewGrid() {
            // If cooling spin, force no winning combinations
            if (gameState.isCoolingSpin) {
                console.log('Generating cooling spin - no wins allowed');
                generateNoWinGrid();
                return;
            }

            // Advanced near-miss logic
            const shouldNearMiss = Math.random() < config.nearMissChance &&
                                   sessionData.consecutiveLosses > 3 &&
                                   !gameState.inFreeSpins &&
                                   sessionData.spinsWithoutScatter > 20;
            if (shouldNearMiss) {
                audioSystem.playAnticipation();
            }

            // Generate the final grid with psychological patterns
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    let symbol;

                    // Near-miss psychology: 2 scatters visible, third just off-screen
                    if (shouldNearMiss && reel < 2 && row === 1 && Math.random() < 0.4) {
                        symbol = '⭐'; // Place scatter in first 2 reels
                        sessionData.nearMissCount++;
                        sessionData.nearMissStreak++;
                        console.log('Near-miss scatter placed - building anticipation');
                    } else if (shouldNearMiss && reel === 2 && row === 0) {
                        symbol = getRandomSymbol(false, true); // No scatter, but exciting symbol
                        console.log('Near-miss completed - player should feel "almost won"');
                    } else {
                        // Normal symbol generation
                        symbol = getRandomSymbol();
                    }

                    symbolGrid[reel][row] = symbol;
                    updateSymbolDisplay(reel, row, symbol);
                }
            }
        }

        function generateNoWinGrid() {
            // Generate a grid with no winning combinations for cooling spin
            const symbols = ['9', '10', 'J', 'Q', 'K']; // Only low symbols, no wilds/scatters

            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    // Ensure no 3+ consecutive symbols
                    let symbol;
                    do {
                        symbol = symbols[Math.floor(Math.random() * symbols.length)];
                    } while (reel >= 2 &&
                             symbolGrid[reel-1] && symbolGrid[reel-2] &&
                             symbolGrid[reel-1][row] === symbol &&
                             symbolGrid[reel-2][row] === symbol);

                    symbolGrid[reel][row] = symbol;
                    updateSymbolDisplay(reel, row, symbol);
                }
            }
        }

        async function cascadeSequence() {
            // Check if this is a cooling spin (forced loss after 5x)
            if (gameState.isCoolingSpin) {
                console.log('Cooling spin - forcing no wins');
                gameState.isCoolingSpin = false;
                gameState.currentMultiplier = 1;
                gameState.consecutiveWins = 0;
                gameState.lastWin = 0;

                // Track losses for psychology
                sessionData.consecutiveLosses++;
                sessionData.smallWinStreak = 0;

                updateDisplay();
                return; // No wins on cooling spin
            }

            let cascadeCount = 0;
            let hasWins = true;
            let totalWinThisSpin = 0;

            while (hasWins) {
                // Fill empty positions
                await fillEmptyPositions();

                // Check for wins
                const wins = checkWins();
                console.log('Wins found:', wins);

                if (wins.length > 0) {
                    cascadeCount++;

                    // Progressive multiplier system (1x → 5x) - More frequent small multipliers
                    if (cascadeCount === 1) {
                        // First win in this spin
                        gameState.consecutiveWins++;
                        gameState.currentMultiplier = Math.min(gameState.consecutiveWins, config.maxMultiplier);

                        // Small win boost - give players more 1x-3x experiences with sound
                        if (gameState.currentMultiplier <= 3 && sessionData.spinsWithoutWin > 5) {
                            console.log(`Small multiplier hit: ${gameState.currentMultiplier}x - player feels winning!`);
                        }

                        // Play multiplier sound for any multiplier increase
                        if (gameState.currentMultiplier > 1) {
                            audioSystem.playMultiplierSound();
                            console.log(`Multiplier increased to ${gameState.currentMultiplier}x - exciting build-up!`);
                        }

                        // Check if we need to set cooling spin for next round
                        if (gameState.currentMultiplier === 5) {
                            gameState.isCoolingSpin = true;
                            console.log('Reached 5x - next spin will be cooling spin');
                        }
                    }

                    // Track session data
                    sessionData.consecutiveLosses = 0;
                    sessionData.smallWinStreak++;
                    sessionData.spinsWithoutWin = 0; // Reset win drought counter

                    // Calculate win amount
                    const winAmount = calculateWinAmount(wins);
                    gameState.lastWin += winAmount;
                    gameState.balance += winAmount;
                    totalWinThisSpin += winAmount;

                    // Track session wins
                    sessionData.totalWon += winAmount;
                    sessionData.netLoss = sessionData.totalWagered - sessionData.totalWon;

                    // Track user activity for wins
                    if (userSystem.isLoggedIn && winAmount > 0) {
                        userSystem.currentUser.balance = gameState.balance;
                        userSystem.trackGameActivity('win', winAmount);
                        userSystem.updateUserDisplay();
                    }

                    // Track big wins for clustering prevention and 5x multiplier usage
                    const winMultiplier = winAmount / gameState.currentBet;
                    if (winMultiplier >= 10) {
                        sessionData.lastBigWinSpin = sessionData.totalSpins;
                        if (gameState.currentMultiplier === 5) {
                            console.log(`BIG WIN with 5x MULTIPLIER! ${winMultiplier.toFixed(1)}x bet using premium cards!`);
                        } else {
                            console.log(`Big win detected: ${winMultiplier.toFixed(1)}x bet`);
                        }
                    }

                    // Loss-Disguised-as-Win (LDW) tracking - More frequent celebrations with EXCITING audio
                    if (winAmount < gameState.currentBet && winAmount >= gameState.currentBet * config.ldwCelebrationThreshold) {
                        sessionData.ldwCount++;
                        console.log(`🎊 LDW CELEBRATION: Won $${winAmount.toFixed(2)} on $${gameState.currentBet.toFixed(2)} bet - PLAYER FEELS LIKE WINNING! 🎊`);

                        // Extra exciting sound for LDW to make player feel amazing
                        setTimeout(() => {
                            audioSystem.playWinSound(winAmount, gameState.currentBet, 1);
                        }, 200);

                        // Add coin drop sound to make LDW feel like real money
                        setTimeout(() => {
                            audioSystem.playCoinDropSound();
                        }, 600);
                    }

                    // Extra celebration for any win during drought
                    if (sessionData.spinsWithoutWin > 10 && winAmount > 0) {
                        console.log(`🎯 DROUGHT BREAKER WIN: $${winAmount.toFixed(2)} - PLAYER RELIEF AND EXCITEMENT! 🎯`);

                        // Play cheer sound for drought breaker
                        setTimeout(() => {
                            audioSystem.playCheerSound();
                        }, 500);

                        // Add extra coin drop for drought breaker
                        setTimeout(() => {
                            audioSystem.playCoinDropSound();
                        }, 800);
                    }

                    // Track free spins wins for balanced returns
                    if (gameState.inFreeSpins) {
                        sessionData.freeSpinsWinTotal += winAmount;
                    }

                    // Update recent win history (last 20 spins)
                    sessionData.recentWinHistory.push(winAmount);
                    if (sessionData.recentWinHistory.length > 20) {
                        sessionData.recentWinHistory.shift();
                    }

                    console.log('Win amount:', winAmount, 'Multiplier:', gameState.currentMultiplier);

                    // Show multiplier if > 1
                    if (gameState.currentMultiplier > 1) {
                        showElement('multiplier-display');
                        document.getElementById('multiplier-value').textContent = `x${gameState.currentMultiplier}`;
                    }

                    // Highlight wins
                    await highlightWins(wins);

                    // Remove winning symbols
                    await removeWinningSymbols(wins);

                    // Check for bonus triggers
                    checkBonusTriggers(wins);

                    updateDisplay();
                } else {
                    hasWins = false;
                }
            }

            // Handle no wins - reset multiplier progression
            if (totalWinThisSpin === 0) {
                gameState.consecutiveWins = 0;
                gameState.currentMultiplier = 1;
                sessionData.consecutiveLosses++;
                sessionData.smallWinStreak = 0;
                sessionData.spinsWithoutWin++;

                // Update recent win history with loss
                sessionData.recentWinHistory.push(0);
                if (sessionData.recentWinHistory.length > 20) {
                    sessionData.recentWinHistory.shift();
                }

                console.log('No wins - reset multiplier to 1x');
            }

            // Show win celebration with enhanced audio
            if (gameState.lastWin > 0) {
                audioSystem.playModernWin(gameState.lastWin, gameState.currentBet);
                showWinCelebration(gameState.lastWin);

                // Extra cheer sound for big wins
                const winMultiplier = gameState.lastWin / gameState.currentBet;
                if (winMultiplier >= 15) {
                    setTimeout(() => {
                        audioSystem.playCheerSound();
                    }, 1000);
                }
            }

            // Hide multiplier if back to base
            if (gameState.currentMultiplier <= 1) {
                hideElement('multiplier-display');
            }
        }

        async function fillEmptyPositions() {
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = config.rows - 1; row >= 0; row--) {
                    if (!symbolGrid[reel][row]) {
                        const symbol = getRandomSymbol();
                        symbolGrid[reel][row] = symbol;

                        const element = document.getElementById(`symbol-${reel}-${row}`);
                        if (element) {
                            element.classList.add('dropping');
                            updateSymbolDisplay(reel, row, symbol);

                            setTimeout(() => {
                                element.classList.remove('dropping');
                            }, 800);
                        }
                    }
                }
            }

            await new Promise(resolve => setTimeout(resolve, 500));
        }

        function checkWins() {
            const wins = [];

            // Check ways-to-win (left to right)
            for (let row = 0; row < config.rows; row++) {
                let count = 1;
                let currentSymbol = symbolGrid[0][row];
                let positions = [{reel: 0, row: row}];

                if (!currentSymbol || currentSymbol === '⭐') continue;

                for (let reel = 1; reel < config.reels; reel++) {
                    const symbol = symbolGrid[reel][row];
                    if (symbol === currentSymbol || symbol === '🃏') {
                        count++;
                        positions.push({reel: reel, row: row});
                    } else {
                        break;
                    }
                }

                if (count >= 3) {
                    wins.push({
                        symbol: currentSymbol,
                        count: count,
                        positions: positions
                    });

                    // Play card match sounds for card symbols in line wins (like Super Ace)
                    if (['J', 'Q', 'K', 'A'].includes(currentSymbol)) {
                        if (count >= 5) {
                            // Special combo sound for 5 cards in a line
                            setTimeout(() => {
                                audioSystem.playCardComboSound(currentSymbol, count);
                            }, 400);
                        } else if (count >= 4) {
                            // Card combo sound for 4 cards
                            setTimeout(() => {
                                audioSystem.playCardComboSound(currentSymbol, count);
                            }, 350);
                        } else {
                            // Regular card match sound for 3 cards
                            setTimeout(() => {
                                audioSystem.playCardMatchSound(currentSymbol, count);
                            }, 300);
                        }
                    }
                }
            }

            // Check for scatter wins
            const scatterPositions = [];
            for (let reel = 0; reel < config.reels; reel++) {
                for (let row = 0; row < config.rows; row++) {
                    if (symbolGrid[reel][row] === '⭐') {
                        scatterPositions.push({reel, row});
                    }
                }
            }

            if (scatterPositions.length >= 3) {
                wins.push({
                    symbol: '⭐',
                    count: scatterPositions.length,
                    positions: scatterPositions
                });

                // 🃏 Play scatter sound - rare and exciting
                setTimeout(() => {
                    audioSystem.playScatterSound();
                }, 100);
            }

            // Additional ChatGPT-style middle row count-based wins for frequent small wins
            const middleRowWins = checkMiddleRowCountWins();
            wins.push(...middleRowWins);

            return wins;
        }

        // ChatGPT-inspired middle row count-based win system for more frequent small wins
        function checkMiddleRowCountWins() {
            const wins = [];
            const middleRow = [];

            // Get middle row (row index 1 for 4-row grid)
            for (let reel = 0; reel < config.reels; reel++) {
                middleRow.push(symbolGrid[reel][1]);
            }

            // Count occurrences of each symbol
            const symbolCounts = {};
            middleRow.forEach(symbol => {
                if (symbol !== '⭐') { // Exclude scatters from count wins
                    symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
                }
            });

            // Check for 3+ same symbols (ChatGPT style) - gives more frequent small wins
            for (const [symbol, count] of Object.entries(symbolCounts)) {
                if (count >= 3) {
                    // Create positions for middle row
                    const positions = [];
                    for (let reel = 0; reel < config.reels; reel++) {
                        if (symbolGrid[reel][1] === symbol) {
                            positions.push({reel, row: 1});
                        }
                    }

                    wins.push({
                        symbol: symbol,
                        count: count,
                        positions: positions,
                        type: 'middle-row-count'
                    });

                    // Play card match sounds for card symbols (like Super Ace)
                    if (['J', 'Q', 'K', 'A'].includes(symbol)) {
                        if (count >= 4) {
                            // Special combo sound for 4+ cards
                            setTimeout(() => {
                                audioSystem.playCardComboSound(symbol, count);
                            }, 200);
                        } else {
                            // Regular card match sound for 3 cards
                            setTimeout(() => {
                                audioSystem.playCardMatchSound(symbol, count);
                            }, 200);
                        }
                    }

                    console.log(`ChatGPT-style count win: ${count}x ${symbol} in middle row - frequent small win!`);
                }
            }

            return wins;
        }

        function calculateWinAmount(wins) {
            let totalWin = 0;

            wins.forEach(win => {
                const payout = config.payouts[win.symbol];
                if (payout && payout[win.count]) {
                    totalWin += payout[win.count] * gameState.currentBet;
                }
            });

            return totalWin * gameState.currentMultiplier;
        }

        async function highlightWins(wins) {
            // Clear previous highlights
            document.querySelectorAll('.symbol').forEach(el => {
                el.classList.remove('winning');
            });

            // Highlight winning symbols
            wins.forEach(win => {
                win.positions.forEach(pos => {
                    const element = document.getElementById(`symbol-${pos.reel}-${pos.row}`);
                    if (element) {
                        element.classList.add('winning');
                    }
                });
            });

            await new Promise(resolve => setTimeout(resolve, 1500));
        }

        async function removeWinningSymbols(wins) {
            const positionsToRemove = new Set();

            wins.forEach(win => {
                win.positions.forEach(pos => {
                    positionsToRemove.add(`${pos.reel}-${pos.row}`);
                });
            });

            // Remove symbols
            positionsToRemove.forEach(posKey => {
                const [reel, row] = posKey.split('-').map(Number);
                symbolGrid[reel][row] = null;

                const element = document.getElementById(`symbol-${reel}-${row}`);
                if (element) {
                    element.textContent = '';
                    element.classList.remove('winning');
                }
            });

            await new Promise(resolve => setTimeout(resolve, 300));
        }

        function checkBonusTriggers(wins) {
            const scatterWin = wins.find(win => win.symbol === '⭐');
            if (scatterWin && scatterWin.count >= 3) {
                sessionData.spinsWithoutScatter = 0; // Reset counter

                if (!gameState.inFreeSpins) {
                    triggerFreeSpins(scatterWin.count);
                } else if (sessionData.freeSpinsRetriggered < config.maxRetriggers) {
                    retriggerFreeSpins();
                }
            }
        }

        function triggerFreeSpins(scatterCount) {
            // Always 10 free spins regardless of scatter count (casino style)
            const spinsAwarded = config.freeSpinsBase;
            gameState.inFreeSpins = true;
            gameState.freeSpinsRemaining = spinsAwarded;
            sessionData.freeSpinsRetriggered = 0; // Reset retrigger count
            sessionData.freeSpinsWinTotal = 0; // Reset free spins win tracking

            // Set up auto-play and guaranteed return
            sessionData.isAutoPlaying = true;
            sessionData.autoPlayRemaining = spinsAwarded;
            sessionData.freeSpinsStartBalance = gameState.balance;
            sessionData.guaranteedReturn = calculateGuaranteedFreeSpinsReturn();
            sessionData.currentFreeSpinWins = 0;

            showElement('freespins-display');
            document.getElementById('freespins-value').textContent = spinsAwarded;

            // Play bonus sound for free spins
            audioSystem.playBonusSound();

            showMessage(`${spinsAwarded} FREE SPINS AWARDED! AUTO-PLAYING WITH SPECIAL EFFECTS!`);

            // Log player state when free spins trigger
            const playerState = getPlayerFinancialState();
            console.log('Free spins triggered - Player balance:', gameState.balance, 'Guaranteed return:', sessionData.guaranteedReturn);

            // Start auto-play sequence
            setTimeout(() => {
                autoPlayFreeSpins();
            }, config.specialEffectDuration);
        }

        // Auto-play free spins with special effects
        async function autoPlayFreeSpins() {
            if (!sessionData.isAutoPlaying || gameState.freeSpinsRemaining <= 0) {
                return;
            }

            console.log(`Auto-playing free spin ${11 - gameState.freeSpinsRemaining}/10 with premium symbols`);

            // Play free spin sound for each auto-play spin
            audioSystem.playFreeSpinSound();

            // Trigger a spin automatically
            await spin();

            // Continue auto-play if more spins remain
            if (gameState.freeSpinsRemaining > 0 && sessionData.isAutoPlaying) {
                setTimeout(() => {
                    autoPlayFreeSpins();
                }, config.autoPlaySpeed);
            }
        }

        function retriggerFreeSpins() {
            // Only +5 spins and max 1 retrigger
            gameState.freeSpinsRemaining += config.freeSpinsRetrigger;
            sessionData.freeSpinsRetriggered++;
            sessionData.autoPlayRemaining += config.freeSpinsRetrigger;

            document.getElementById('freespins-value').textContent = gameState.freeSpinsRemaining;
            showMessage(`+${config.freeSpinsRetrigger} FREE SPINS!`);
        }

        // Betting Functions
        function decreaseBet() {
            if (gameState.isSpinning || gameState.inFreeSpins) return;

            if (currentBetIndex > 0) {
                currentBetIndex--;
                gameState.currentBet = config.betLevels[currentBetIndex];
                updateDisplay();
            }
        }

        function increaseBet() {
            if (gameState.isSpinning || gameState.inFreeSpins) return;

            if (currentBetIndex < config.betLevels.length - 1 && gameState.balance >= config.betLevels[currentBetIndex + 1]) {
                currentBetIndex++;
                gameState.currentBet = config.betLevels[currentBetIndex];
                updateDisplay();
            }
        }

        function setMaxBet() {
            if (gameState.isSpinning || gameState.inFreeSpins) return;

            // Find highest affordable bet
            let maxIndex = 0;
            for (let i = config.betLevels.length - 1; i >= 0; i--) {
                if (gameState.balance >= config.betLevels[i]) {
                    maxIndex = i;
                    break;
                }
            }

            currentBetIndex = maxIndex;
            gameState.currentBet = config.betLevels[currentBetIndex];
            updateDisplay();
            console.log('Max bet set to:', gameState.currentBet);
        }

        // 🎵 Howler.js Music Toggle Functionality - Complete Audio Control
        function toggleBackgroundMusic() {
            const musicButton = document.getElementById('music-toggle');

            // Use the audioSystem's built-in toggle method
            const isEnabled = audioSystem.toggleMusic();

            if (isEnabled) {
                musicButton.textContent = '🎼 MUSIC ON';
                console.log('🎵 HOWLER.JS MUSIC ENABLED - All casino sounds active!');
            } else {
                musicButton.textContent = '🔇 MUSIC OFF';
                console.log('🔇 HOWLER.JS MUSIC DISABLED - Silent mode');
            }
        }

        // UI Functions
        function updateDisplay() {
            // Sync user balance if logged in
            if (userSystem.isLoggedIn && userSystem.currentUser) {
                userSystem.currentUser.balance = gameState.balance;
                userSystem.saveUser(userSystem.currentUser);
            }

            document.getElementById('balance').textContent = `$${gameState.balance.toFixed(2)}`;
            document.getElementById('current-bet').textContent = `$${gameState.currentBet.toFixed(2)}`;
            document.getElementById('bet-display').textContent = `$${gameState.currentBet.toFixed(2)}`;
            document.getElementById('last-win').textContent = `$${gameState.lastWin.toFixed(2)}`;

            // Update header multiplier display
            document.getElementById('header-multiplier-value').textContent = `${gameState.currentMultiplier}X`;

            const spinButton = document.getElementById('spin-button');
            const spinText = document.getElementById('spin-text');

            if (gameState.isSpinning) {
                spinButton.disabled = true;
                spinText.textContent = 'SPINNING...';
            } else if (gameState.inFreeSpins) {
                spinButton.disabled = false;
                spinText.textContent = `FREE SPIN (${gameState.freeSpinsRemaining})`;
            } else {
                spinButton.disabled = gameState.balance < gameState.currentBet;
                spinText.textContent = 'SPIN';
            }
        }

        function showWinCelebration(amount) {
            const winMultiplier = amount / gameState.currentBet;
            let winType = 'WIN!';

            if (winMultiplier >= 50) {
                winType = 'MEGA WIN!';
            } else if (winMultiplier >= 25) {
                winType = 'SUPER WIN!';
            } else if (winMultiplier >= 10) {
                winType = 'BIG WIN!';
            }

            document.getElementById('win-amount-display').textContent = `$${amount.toFixed(2)}`;
            document.getElementById('win-type-display').textContent = winType;
            showElement('win-popup');

            setTimeout(() => {
                hideElement('win-popup');
            }, 3000);
        }

        function showMessage(text) {
            const messageEl = document.getElementById('message-display');
            messageEl.textContent = text;
            showElement('message-display');

            setTimeout(() => {
                hideElement('message-display');
            }, 3000);
        }

        function showElement(id) {
            document.getElementById(id).classList.remove('hidden');
        }

        function hideElement(id) {
            document.getElementById(id).classList.add('hidden');
        }

        // Start the game when page loads
        window.addEventListener('load', () => {
            console.log('Page loaded, starting game...');
            initGame();
        });
    </script>
</body>
</html>

