/**
 * WinCalculator.js - Ways-to-win calculation and cascading logic
 * Handles win detection, calculation, and cascading mechanics
 */

class WinCalculator {
    /**
     * Initialize the win calculator
     * @param {Phaser.Scene} scene - The Phaser scene
     * @param {Object} config - Game configuration
     */
    constructor(scene, config) {
        this.scene = scene;
        this.config = config;

        // Win calculation settings
        this.minWinLength = 3;
        this.maxWinLength = 5;
        this.waysToWin = 1024; // 4^5 = 1024 ways

        // Cache for performance
        this.winCache = new Map();
        this.adjacencyCache = new Map();
    }

    /**
     * Calculate all wins in the current symbol grid
     * @param {Array} symbolGrid - 2D array of symbols [reel][row]
     * @returns {Array} Array of win objects
     */
    calculateWins(symbolGrid) {
        if (!this.validateGrid(symbolGrid)) {
            return [];
        }

        const wins = [];
        const processedPositions = new Set();

        // Check for ways-to-win starting from leftmost reel
        for (let startRow = 0; startRow < this.config.rows; startRow++) {
            const startSymbol = symbolGrid[0][startRow];
            if (!startSymbol) continue;

            const winPath = this.findWinPath(symbolGrid, 0, startRow, processedPositions);

            if (winPath && winPath.length >= this.minWinLength) {
                const winData = this.createWinData(winPath, startSymbol, symbolGrid);
                if (winData) {
                    wins.push(winData);

                    // Mark positions as processed
                    winPath.forEach(pos => {
                        processedPositions.add(`${pos.reel}-${pos.row}`);
                    });
                }
            }
        }

        // Check for scatter wins (can appear anywhere)
        const scatterWin = this.calculateScatterWin(symbolGrid);
        if (scatterWin) {
            wins.push(scatterWin);
        }

        return wins;
    }

    /**
     * Find winning path using ways-to-win logic
     * @param {Array} symbolGrid - Symbol grid
     * @param {number} startReel - Starting reel
     * @param {number} startRow - Starting row
     * @param {Set} processedPositions - Already processed positions
     * @returns {Array} Array of winning positions
     */
    findWinPath(symbolGrid, startReel, startRow, processedPositions) {
        const startSymbol = symbolGrid[startReel][startRow];
        const path = [{ reel: startReel, row: startRow }];

        // Use dynamic programming approach for efficiency
        const memo = new Map();

        return this.findWinPathRecursive(
            symbolGrid,
            startSymbol,
            startReel + 1,
            path,
            processedPositions,
            memo
        );
    }

    /**
     * Recursive function to find win path
     * @param {Array} symbolGrid - Symbol grid
     * @param {string} targetSymbol - Symbol we're matching
     * @param {number} currentReel - Current reel being checked
     * @param {Array} currentPath - Current path of positions
     * @param {Set} processedPositions - Processed positions
     * @param {Map} memo - Memoization cache
     * @returns {Array} Complete win path or null
     */
    findWinPathRecursive(symbolGrid, targetSymbol, currentReel, currentPath, processedPositions, memo) {
        // Base case: reached end of reels
        if (currentReel >= this.config.reels) {
            return currentPath.length >= this.minWinLength ? currentPath : null;
        }

        // Create cache key
        const cacheKey = `${targetSymbol}-${currentReel}-${currentPath.length}`;
        if (memo.has(cacheKey)) {
            return memo.get(cacheKey);
        }

        let bestPath = null;
        let foundMatch = false;

        // Check all positions in current reel
        for (let row = 0; row < this.config.rows; row++) {
            const currentSymbol = symbolGrid[currentReel][row];
            const posKey = `${currentReel}-${row}`;

            // Skip if position already processed or no symbol
            if (!currentSymbol || processedPositions.has(posKey)) {
                continue;
            }

            // Check if symbols match (including wild substitution)
            if (this.symbolsMatch(targetSymbol, currentSymbol)) {
                foundMatch = true;
                const newPath = [...currentPath, { reel: currentReel, row: row }];

                // Continue to next reel
                const completePath = this.findWinPathRecursive(
                    symbolGrid,
                    targetSymbol,
                    currentReel + 1,
                    newPath,
                    processedPositions,
                    memo
                );

                if (completePath && (!bestPath || completePath.length > bestPath.length)) {
                    bestPath = completePath;
                }
            }
        }

        // If no match found in current reel, return current path if it's long enough
        if (!foundMatch && currentPath.length >= this.minWinLength) {
            bestPath = currentPath;
        }

        memo.set(cacheKey, bestPath);
        return bestPath;
    }

    /**
     * Check if two symbols match (including wild substitution)
     * @param {string} symbol1 - First symbol
     * @param {string} symbol2 - Second symbol
     * @returns {boolean} True if symbols match
     */
    symbolsMatch(symbol1, symbol2) {
        // Get symbols manager from scene
        const symbolsManager = this.scene.symbolsManager;

        if (!symbolsManager) {
            return symbol1 === symbol2;
        }

        return symbolsManager.symbolsMatch(symbol1, symbol2);
    }

    /**
     * Calculate scatter wins
     * @param {Array} symbolGrid - Symbol grid
     * @returns {Object|null} Scatter win data or null
     */
    calculateScatterWin(symbolGrid) {
        const scatterPositions = [];

        // Find all scatter positions
        for (let reel = 0; reel < this.config.reels; reel++) {
            for (let row = 0; row < this.config.rows; row++) {
                if (symbolGrid[reel][row] === 'SCATTER') {
                    scatterPositions.push({ reel, row });
                }
            }
        }

        // Need at least 3 scatters for a win
        if (scatterPositions.length >= 3) {
            return {
                symbol: 'SCATTER',
                positions: scatterPositions,
                count: scatterPositions.length,
                type: 'scatter',
                payout: this.getScatterPayout(scatterPositions.length)
            };
        }

        return null;
    }

    /**
     * Get scatter payout multiplier
     * @param {number} count - Number of scatters
     * @returns {number} Payout multiplier
     */
    getScatterPayout(count) {
        const scatterPayouts = {
            3: 2,
            4: 5,
            5: 50
        };

        return scatterPayouts[count] || 0;
    }

    /**
     * Create win data object
     * @param {Array} positions - Array of winning positions
     * @param {string} symbol - Winning symbol
     * @param {Array} symbolGrid - Symbol grid
     * @returns {Object} Win data object
     */
    createWinData(positions, symbol, symbolGrid) {
        if (!positions || positions.length < this.minWinLength) {
            return null;
        }

        // Get actual symbol (in case of wild substitution)
        const actualSymbol = this.getActualSymbol(symbol, symbolGrid);

        return {
            symbol: actualSymbol,
            positions: positions,
            count: positions.length,
            type: 'ways',
            payout: this.getSymbolPayout(actualSymbol, positions.length)
        };
    }

    /**
     * Get actual symbol for payout calculation
     * @param {string} symbol - Original symbol
     * @param {Array} symbolGrid - Symbol grid
     * @returns {string} Actual symbol for payout
     */
    getActualSymbol(symbol, symbolGrid) {
        const symbolsManager = this.scene.symbolsManager;

        if (!symbolsManager || !symbolsManager.isWild(symbol)) {
            return symbol;
        }

        // For wild symbols, find the highest paying non-wild symbol in the win
        // This is a simplified approach - could be more sophisticated
        return symbol;
    }

    /**
     * Get symbol payout multiplier
     * @param {string} symbol - Symbol name
     * @param {number} count - Number of symbols
     * @returns {number} Payout multiplier
     */
    getSymbolPayout(symbol, count) {
        const symbolsManager = this.scene.symbolsManager;

        if (symbolsManager) {
            return symbolsManager.getSymbolPayout(symbol, count);
        }

        // Fallback payout table
        const fallbackPayouts = {
            '9': [0, 0, 5, 25, 100],
            '10': [0, 0, 5, 30, 125],
            'J': [0, 0, 10, 40, 150],
            'Q': [0, 0, 10, 50, 200],
            'K': [0, 0, 15, 75, 300],
            'A': [0, 0, 20, 100, 500],
            'GOLDEN_CARD': [0, 0, 50, 200, 1000],
            'LITTLE_JOKER': [0, 0, 50, 200, 1000],
            'BIG_JOKER': [0, 0, 50, 200, 1000]
        };

        const payouts = fallbackPayouts[symbol];
        return payouts ? (payouts[count] || 0) : 0;
    }

    /**
     * Calculate total win amount
     * @param {Array} wins - Array of win objects
     * @param {number} bet - Current bet amount
     * @param {number} multiplier - Current multiplier
     * @returns {number} Total win amount
     */
    calculateWinAmount(wins, bet, multiplier = 1) {
        let totalWin = 0;

        wins.forEach(win => {
            const basePayout = win.payout * bet;
            totalWin += basePayout;
        });

        return totalWin * multiplier;
    }

    /**
     * Validate symbol grid
     * @param {Array} symbolGrid - Symbol grid to validate
     * @returns {boolean} True if valid
     */
    validateGrid(symbolGrid) {
        if (!Array.isArray(symbolGrid)) return false;
        if (symbolGrid.length !== this.config.reels) return false;

        for (let reel = 0; reel < this.config.reels; reel++) {
            if (!Array.isArray(symbolGrid[reel])) return false;
            if (symbolGrid[reel].length !== this.config.rows) return false;
        }

        return true;
    }

    /**
     * Get win statistics
     * @param {Array} wins - Array of wins
     * @returns {Object} Win statistics
     */
    getWinStatistics(wins) {
        if (!wins || wins.length === 0) {
            return {
                totalWins: 0,
                totalWays: 0,
                scatterWins: 0,
                biggestWin: 0,
                averageWin: 0
            };
        }

        const stats = {
            totalWins: wins.length,
            totalWays: 0,
            scatterWins: 0,
            biggestWin: 0,
            averageWin: 0
        };

        let totalPayout = 0;

        wins.forEach(win => {
            if (win.type === 'ways') {
                stats.totalWays++;
            } else if (win.type === 'scatter') {
                stats.scatterWins++;
            }

            if (win.payout > stats.biggestWin) {
                stats.biggestWin = win.payout;
            }

            totalPayout += win.payout;
        });

        stats.averageWin = totalPayout / wins.length;

        return stats;
    }

    /**
     * Check for cascading opportunities
     * @param {Array} symbolGrid - Current symbol grid
     * @returns {boolean} True if cascading should continue
     */
    shouldCascade(symbolGrid) {
        const wins = this.calculateWins(symbolGrid);
        return wins.length > 0;
    }

    /**
     * Get positions to remove for cascading
     * @param {Array} wins - Array of wins
     * @returns {Set} Set of positions to remove
     */
    getPositionsToRemove(wins) {
        const positionsToRemove = new Set();

        wins.forEach(win => {
            win.positions.forEach(pos => {
                positionsToRemove.add(`${pos.reel}-${pos.row}`);
            });
        });

        return positionsToRemove;
    }

    /**
     * Clear win cache
     */
    clearCache() {
        this.winCache.clear();
        this.adjacencyCache.clear();
    }
}
