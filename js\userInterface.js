class UserInterface {
    constructor(userManager) {
        this.userManager = userManager;
        this.createUIElements();
        this.setupEventListeners();
    }

    createUIElements() {
        // Create main container
        const container = document.createElement('div');
        container.id = 'user-interface';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
            color: white;
            font-family: Arial, sans-serif;
            z-index: 1000;
        `;

        // Create login form
        const loginForm = document.createElement('div');
        loginForm.id = 'login-form';
        loginForm.innerHTML = `
            <h3>Login</h3>
            <input type="text" id="mobile-number" placeholder="Mobile Number" style="margin: 5px 0; padding: 5px;">
            <input type="password" id="password" placeholder="Password" style="margin: 5px 0; padding: 5px;">
            <button id="login-btn" style="margin: 5px 0; padding: 5px;">Login</button>
            <button id="register-btn" style="margin: 5px 0; padding: 5px;">Register</button>
        `;

        // Create profile section
        const profileSection = document.createElement('div');
        profileSection.id = 'profile-section';
        profileSection.style.display = 'none';
        profileSection.innerHTML = `
            <h3>Profile</h3>
            <div id="balance-display">Balance: $0</div>
            <div style="margin: 10px 0;">
                <input type="number" id="deposit-amount" placeholder="Amount" style="margin: 5px 0; padding: 5px;">
                <button id="deposit-btn" style="margin: 5px 0; padding: 5px;">Deposit</button>
            </div>
            <div style="margin: 10px 0;">
                <input type="number" id="withdraw-amount" placeholder="Amount" style="margin: 5px 0; padding: 5px;">
                <button id="withdraw-btn" style="margin: 5px 0; padding: 5px;">Withdraw</button>
            </div>
            <button id="logout-btn" style="margin: 5px 0; padding: 5px;">Logout</button>
            <div id="transaction-history" style="margin-top: 10px;">
                <h4>Transaction History</h4>
                <div id="transactions-list"></div>
            </div>
        `;

        container.appendChild(loginForm);
        container.appendChild(profileSection);
        document.body.appendChild(container);
    }

    setupEventListeners() {
        // Login button
        document.getElementById('login-btn').addEventListener('click', () => {
            const mobileNumber = document.getElementById('mobile-number').value;
            const password = document.getElementById('password').value;
            const result = this.userManager.login(mobileNumber, password);
            
            if (result.success) {
                this.showProfile();
            } else {
                alert(result.message);
            }
        });

        // Register button
        document.getElementById('register-btn').addEventListener('click', () => {
            const mobileNumber = document.getElementById('mobile-number').value;
            const password = document.getElementById('password').value;
            const result = this.userManager.createAccount(mobileNumber, password);
            
            if (result.success) {
                alert('Account created successfully! Please login.');
            } else {
                alert(result.message);
            }
        });

        // Deposit button
        document.getElementById('deposit-btn').addEventListener('click', () => {
            const amount = parseFloat(document.getElementById('deposit-amount').value);
            if (isNaN(amount) || amount <= 0) {
                alert('Please enter a valid amount');
                return;
            }
            
            const result = this.userManager.deposit(amount);
            if (result.success) {
                this.updateBalance();
                this.updateTransactionHistory();
                document.getElementById('deposit-amount').value = '';
            } else {
                alert(result.message);
            }
        });

        // Withdraw button
        document.getElementById('withdraw-btn').addEventListener('click', () => {
            const amount = parseFloat(document.getElementById('withdraw-amount').value);
            if (isNaN(amount) || amount <= 0) {
                alert('Please enter a valid amount');
                return;
            }
            
            const result = this.userManager.withdraw(amount);
            if (result.success) {
                this.updateBalance();
                this.updateTransactionHistory();
                document.getElementById('withdraw-amount').value = '';
            } else {
                alert(result.message);
            }
        });

        // Logout button
        document.getElementById('logout-btn').addEventListener('click', () => {
            this.userManager.logout();
            this.showLogin();
        });
    }

    showProfile() {
        document.getElementById('login-form').style.display = 'none';
        document.getElementById('profile-section').style.display = 'block';
        this.updateBalance();
        this.updateTransactionHistory();
    }

    showLogin() {
        document.getElementById('login-form').style.display = 'block';
        document.getElementById('profile-section').style.display = 'none';
        document.getElementById('mobile-number').value = '';
        document.getElementById('password').value = '';
    }

    updateBalance() {
        const balance = this.userManager.getCurrentBalance();
        document.getElementById('balance-display').textContent = `Balance: $${balance.toFixed(2)}`;
    }

    updateTransactionHistory() {
        const transactions = this.userManager.getUserTransactions();
        const transactionsList = document.getElementById('transactions-list');
        transactionsList.innerHTML = '';

        transactions.slice(-5).reverse().forEach(transaction => {
            const transactionElement = document.createElement('div');
            transactionElement.style.cssText = `
                padding: 5px;
                border-bottom: 1px solid #444;
                font-size: 12px;
            `;
            const date = new Date(transaction.timestamp).toLocaleString();
            transactionElement.textContent = `${date} - ${transaction.type.toUpperCase()}: $${transaction.amount.toFixed(2)}`;
            transactionsList.appendChild(transactionElement);
        });
    }
} 