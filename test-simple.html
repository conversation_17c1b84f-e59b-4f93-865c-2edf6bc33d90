<!DOCTYPE html>
<html>
<head>
    <title>Simple Test</title>
    <style>
        body { font-family: Arial; background: #1a1a2e; color: white; padding: 20px; }
        .container { max-width: 400px; margin: 0 auto; background: #16213e; padding: 30px; border-radius: 10px; }
        input { width: 100%; padding: 12px; margin: 10px 0; border: 2px solid #FFD700; border-radius: 5px; background: #0f3460; color: white; }
        button { width: 100%; padding: 15px; background: #FFD700; color: #1a1a2e; border: none; border-radius: 5px; font-size: 18px; font-weight: bold; cursor: pointer; margin: 10px 0; }
        .test { background: #FF6B35; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎰 SIMPLE TEST 🎰</h1>
        
        <input type="text" id="name" placeholder="Full Name" value="Test User">
        <input type="tel" id="mobile" placeholder="Mobile Number" value="**********">
        <input type="password" id="password" placeholder="Password" value="password123">
        <input type="password" id="confirm" placeholder="Confirm Password" value="password123">
        
        <button onclick="register()">🎊 CREATE ACCOUNT</button>
        <button class="test" onclick="alert('Test works!')">🧪 TEST BUTTON</button>
        
        <div id="result"></div>
    </div>

    <script>
        function register() {
            console.log('🔥 REGISTER CALLED');
            alert('Register button clicked!');
            
            const name = document.getElementById('name').value;
            const mobile = document.getElementById('mobile').value;
            const password = document.getElementById('password').value;
            const confirm = document.getElementById('confirm').value;
            
            if (!name || !mobile || !password || !confirm) {
                alert('❌ Fill all fields!');
                return;
            }
            
            if (password !== confirm) {
                alert('❌ Passwords do not match!');
                return;
            }
            
            const user = {
                id: 'VA' + Date.now(),
                name: name,
                mobile: mobile,
                password: password,
                balance: 1000.00
            };
            
            localStorage.setItem('testUser', JSON.stringify(user));
            alert('🎊 Account created successfully!');
            
            document.getElementById('result').innerHTML = `
                <h3>✅ Success!</h3>
                <p>Name: ${user.name}</p>
                <p>Mobile: ${user.mobile}</p>
                <p>Balance: $${user.balance}</p>
            `;
        }
    </script>
</body>
</html>
