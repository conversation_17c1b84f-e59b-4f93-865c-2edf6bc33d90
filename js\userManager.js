class UserManager {
    constructor() {
        this.currentUser = null;
        this.users = JSON.parse(localStorage.getItem('slotGameUsers')) || {};
        this.transactions = JSON.parse(localStorage.getItem('slotGameTransactions')) || {};
    }

    // Create new user account
    createAccount(mobileNumber, password) {
        if (this.users[mobileNumber]) {
            return { success: false, message: 'Account already exists' };
        }

        const userId = 'USER_' + Date.now();
        this.users[mobileNumber] = {
            userId,
            password,
            balance: 0,
            createdAt: new Date().toISOString()
        };

        this.saveUsers();
        return { success: true, userId };
    }

    // Login user
    login(mobileNumber, password) {
        const user = this.users[mobileNumber];
        if (!user || user.password !== password) {
            return { success: false, message: 'Invalid credentials' };
        }

        this.currentUser = user;
        return { success: true, user };
    }

    // Logout user
    logout() {
        this.currentUser = null;
    }

    // Deposit money
    deposit(amount) {
        if (!this.currentUser) {
            return { success: false, message: 'No user logged in' };
        }

        const mobileNumber = Object.keys(this.users).find(key => 
            this.users[key].userId === this.currentUser.userId
        );

        this.users[mobileNumber].balance += amount;
        this.recordTransaction('deposit', amount);
        this.saveUsers();
        
        return { 
            success: true, 
            newBalance: this.users[mobileNumber].balance 
        };
    }

    // Withdraw money
    withdraw(amount) {
        if (!this.currentUser) {
            return { success: false, message: 'No user logged in' };
        }

        const mobileNumber = Object.keys(this.users).find(key => 
            this.users[key].userId === this.currentUser.userId
        );

        if (this.users[mobileNumber].balance < amount) {
            return { success: false, message: 'Insufficient balance' };
        }

        this.users[mobileNumber].balance -= amount;
        this.recordTransaction('withdraw', amount);
        this.saveUsers();
        
        return { 
            success: true, 
            newBalance: this.users[mobileNumber].balance 
        };
    }

    // Record transaction
    recordTransaction(type, amount) {
        if (!this.currentUser) return;

        const transaction = {
            userId: this.currentUser.userId,
            type,
            amount,
            timestamp: new Date().toISOString()
        };

        if (!this.transactions[this.currentUser.userId]) {
            this.transactions[this.currentUser.userId] = [];
        }

        this.transactions[this.currentUser.userId].push(transaction);
        localStorage.setItem('slotGameTransactions', JSON.stringify(this.transactions));
    }

    // Get user transactions
    getUserTransactions() {
        if (!this.currentUser) return [];
        return this.transactions[this.currentUser.userId] || [];
    }

    // Save users to localStorage
    saveUsers() {
        localStorage.setItem('slotGameUsers', JSON.stringify(this.users));
    }

    // Get current user balance
    getCurrentBalance() {
        if (!this.currentUser) return 0;
        const mobileNumber = Object.keys(this.users).find(key => 
            this.users[key].userId === this.currentUser.userId
        );
        return this.users[mobileNumber].balance;
    }
} 