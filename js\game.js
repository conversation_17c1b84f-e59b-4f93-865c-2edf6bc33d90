class Game extends Phaser.Scene {
    constructor() {
        super({ key: 'Game' });
        this.userManager = new UserManager();
        this.userInterface = null;
    }

    create() {
        // Initialize user interface
        this.userInterface = new UserInterface(this.userManager);

        // Modify balance display to use user balance
        this.balanceText = this.add.text(16, 16, 'Balance: $0', {
            fontSize: '32px',
            fill: '#fff'
        });

        // Update balance display
        this.updateBalanceDisplay();
    }

    updateBalanceDisplay() {
        const balance = this.userManager.getCurrentBalance();
        this.balanceText.setText(`Balance: $${balance.toFixed(2)}`);
    }

    placeBet() {
        if (!this.userManager.currentUser) {
            alert('Please login to play');
            return;
        }

        const currentBalance = this.userManager.getCurrentBalance();
        if (currentBalance < this.betAmount) {
            alert('Insufficient balance');
            return;
        }

        // Withdraw bet amount
        const result = this.userManager.withdraw(this.betAmount);
        if (!result.success) {
            alert('Failed to place bet');
            return;
        }

        this.updateBalanceDisplay();
        this.startSpin();
    }

    handleWin(winAmount) {
        // Deposit winnings
        const result = this.userManager.deposit(winAmount);
        if (result.success) {
            this.updateBalanceDisplay();
        }
    }
} 