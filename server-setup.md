# 🔐 VEGAS ACE SLOTS - SERVER SETUP GUIDE

## ✅ **FIXED ISSUES:**

### **1. Login Form Shows First** ✅
- Users now see LOGIN form when they open the page
- Clean, professional interface without test buttons

### **2. Register Button Works** ✅
- "Register here" link properly switches to registration form
- All form validation works correctly

### **3. Backend File Storage** ✅
- No more browser localStorage
- User data saved to `users_data.json` file on server
- Secure password hashing with PHP

### **4. Clean Production Code** ✅
- Removed all test/demo buttons and data
- Professional authentication flow
- Proper error handling and validation

## 🚀 **HOW TO RUN:**

### **Option 1: Local PHP Server**
```bash
# Navigate to your project folder
cd "C:\Users\<USER>\Desktop\super ace"

# Start PHP built-in server
php -S localhost:8000

# Open browser and go to:
# http://localhost:8000/working-slots.html
```

### **Option 2: XAMPP/WAMP**
1. Copy all files to your XAMPP/WAMP `htdocs` folder
2. Start Apache server
3. Open: `http://localhost/working-slots.html`

## 📁 **FILES CREATED:**

1. **`working-slots.html`** - Main game file (updated)
2. **`users-api.php`** - Backend API for user authentication
3. **`users_data.json`** - Will be created automatically to store user data

## 🔄 **USER FLOW:**

1. **User opens page** → Sees LOGIN form
2. **New user clicks "Register here"** → Switches to REGISTER form
3. **User fills registration** → Data saved to backend file
4. **User can login** → Data verified from backend file
5. **Successful login** → Enters game with their balance

## 🛡️ **SECURITY FEATURES:**

- ✅ Password hashing (not stored in plain text)
- ✅ Input validation and sanitization
- ✅ SQL injection protection (using JSON files)
- ✅ CORS headers for API security
- ✅ Server-side data storage

## 🎯 **API ENDPOINTS:**

- **Register:** `users-api.php?action=register`
- **Login:** `users-api.php?action=login`

## 📊 **USER DATA STRUCTURE:**
```json
{
  "id": "***************",
  "name": "John Doe",
  "mobile": "**********",
  "email": "<EMAIL>",
  "password": "$2y$10$...", // Hashed
  "balance": 1000.00,
  "joinDate": "2024-01-01 12:00:00",
  "lastLogin": "2024-01-01 12:00:00",
  "totalDeposits": 0,
  "totalWithdrawals": 0,
  "totalSpins": 0,
  "totalWins": 0,
  "isActive": true
}
```

## 🧪 **TESTING:**

1. **Open the page** - Should show login form
2. **Click "Register here"** - Should switch to register form
3. **Fill registration form** - Should create account and enter game
4. **Try logging in** - Should work with created credentials

## ⚠️ **REQUIREMENTS:**

- PHP 7.0+ (for password hashing)
- Web server (Apache/Nginx or PHP built-in)
- Write permissions for creating `users_data.json`

## 🔧 **TROUBLESHOOTING:**

**If registration doesn't work:**
- Check if PHP server is running
- Verify file permissions for writing `users_data.json`
- Check browser console for error messages

**If forms don't switch:**
- Clear browser cache
- Check if JavaScript is enabled
- Verify all files are in same directory
