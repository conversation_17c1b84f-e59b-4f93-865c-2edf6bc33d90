<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Authentication System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        input {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>🔐 Vegas Ace Slots - Authentication Test</h1>

    <div class="test-section">
        <h2>📝 Register New User</h2>
        <input type="text" id="reg-name" placeholder="Full Name" value="Test User">
        <input type="tel" id="reg-mobile" placeholder="Mobile Number" value="**********">
        <input type="email" id="reg-email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="reg-password" placeholder="Password" value="password123">
        <button onclick="testRegister()">Register User</button>
        <div id="register-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>🔑 Login User</h2>
        <input type="tel" id="login-mobile" placeholder="Mobile Number" value="**********">
        <input type="password" id="login-password" placeholder="Password" value="password123">
        <button onclick="testLogin()">Login User</button>
        <div id="login-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>💳 Test Deposit</h2>
        <input type="text" id="deposit-user-id" placeholder="User ID" readonly>
        <input type="number" id="deposit-amount" placeholder="Deposit Amount" value="100">
        <button onclick="testDeposit()">Make Deposit</button>
        <div id="deposit-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>💸 Test Withdrawal</h2>
        <input type="text" id="withdraw-user-id" placeholder="User ID" readonly>
        <input type="number" id="withdraw-amount" placeholder="Withdrawal Amount" value="50">
        <button onclick="testWithdraw()">Make Withdrawal</button>
        <div id="withdraw-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>📊 Get Analytics</h2>
        <input type="text" id="analytics-user-id" placeholder="User ID" readonly>
        <button onclick="testAnalytics()">Get User Analytics</button>
        <div id="analytics-result" class="result" style="display: none;"></div>
    </div>

    <script>
        let currentUserId = null;

        async function testRegister() {
            const data = {
                name: document.getElementById('reg-name').value,
                mobile: document.getElementById('reg-mobile').value,
                email: document.getElementById('reg-email').value,
                password: document.getElementById('reg-password').value
            };

            try {
                const response = await fetch('users-api.php?action=register', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                const resultDiv = document.getElementById('register-result');
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = `<strong>Result:</strong> ${JSON.stringify(result, null, 2)}`;

                if (result.success) {
                    currentUserId = result.user.id;
                    updateUserIds();
                }
            } catch (error) {
                console.error('Error:', error);
            }
        }

        async function testLogin() {
            const data = {
                mobile: document.getElementById('login-mobile').value,
                password: document.getElementById('login-password').value
            };

            try {
                const response = await fetch('users-api.php?action=login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                const resultDiv = document.getElementById('login-result');
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = `<strong>Result:</strong> ${JSON.stringify(result, null, 2)}`;

                if (result.success) {
                    currentUserId = result.user.id;
                    updateUserIds();
                }
            } catch (error) {
                console.error('Error:', error);
            }
        }

        async function testDeposit() {
            if (!currentUserId) {
                alert('Please login first!');
                return;
            }

            const data = {
                user_id: currentUserId,
                amount: parseFloat(document.getElementById('deposit-amount').value),
                method: 'card'
            };

            try {
                const response = await fetch('users-api.php?action=deposit', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                const resultDiv = document.getElementById('deposit-result');
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = `<strong>Result:</strong> ${JSON.stringify(result, null, 2)}`;
            } catch (error) {
                console.error('Error:', error);
            }
        }

        async function testWithdraw() {
            if (!currentUserId) {
                alert('Please login first!');
                return;
            }

            const data = {
                user_id: currentUserId,
                amount: parseFloat(document.getElementById('withdraw-amount').value),
                bankAccount: '**********',
                bankName: 'Test Bank'
            };

            try {
                const response = await fetch('users-api.php?action=withdraw', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                const resultDiv = document.getElementById('withdraw-result');
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = `<strong>Result:</strong> ${JSON.stringify(result, null, 2)}`;
            } catch (error) {
                console.error('Error:', error);
            }
        }

        async function testAnalytics() {
            if (!currentUserId) {
                alert('Please login first!');
                return;
            }

            const data = { user_id: currentUserId };

            try {
                const response = await fetch('users-api.php?action=get_analytics', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                const resultDiv = document.getElementById('analytics-result');
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = `<strong>Result:</strong> ${JSON.stringify(result, null, 2)}`;
            } catch (error) {
                console.error('Error:', error);
            }
        }

        function updateUserIds() {
            document.getElementById('deposit-user-id').value = currentUserId;
            document.getElementById('withdraw-user-id').value = currentUserId;
            document.getElementById('analytics-user-id').value = currentUserId;
        }
    </script>
</body>
</html>
