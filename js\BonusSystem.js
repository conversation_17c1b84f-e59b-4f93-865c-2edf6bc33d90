/**
 * BonusSystem.js - Free spins and wild transformation logic
 * Handles bonus features, free spins, and special symbol transformations
 */

class BonusSystem {
    /**
     * Initialize the bonus system
     * @param {Phaser.Scene} scene - The Phaser scene
     * @param {Object} config - Game configuration
     * @param {Object} gameState - Game state object
     */
    constructor(scene, config, gameState) {
        this.scene = scene;
        this.config = config;
        this.gameState = gameState;

        // Free spins configuration
        this.freeSpinsConfig = {
            triggerScatters: 3,
            retriggerScatters: 3,
            initialSpins: 10,
            retriggerSpins: 5,
            maxSpins: 50,
            baseMultiplier: 2,
            maxMultiplier: 10,
            multiplierIncrement: 2
        };

        // Wild transformation configuration
        this.wildTransformConfig = {
            goldenToLittleJoker: 0.05,  // 5% chance
            goldenToBigJoker: 0.02,     // 2% chance
            transformOnNonWin: true,    // Only transform on non-winning spins
            maxTransformsPerSpin: 2     // Maximum transformations per spin
        };

        // Bonus tracking
        this.bonusHistory = [];
        this.currentBonusRound = null;
    }

    /**
     * Check for bonus triggers after a spin
     * @param {Array} symbolGrid - Current symbol grid
     * @param {Array} wins - Current wins
     */
    checkBonusTriggers(symbolGrid, wins) {
        // Check for free spins trigger
        this.checkFreeSpinsTrigger(symbolGrid);

        // Check for wild transformations (only on non-winning spins)
        if (wins.length === 0) {
            this.checkWildTransformations(symbolGrid);
        }

        // Check for retrigger during free spins
        if (this.gameState.inFreeSpins) {
            this.checkFreeSpinsRetrigger(symbolGrid);
        }
    }

    /**
     * Check for free spins trigger
     * @param {Array} symbolGrid - Symbol grid
     */
    checkFreeSpinsTrigger(symbolGrid) {
        if (this.gameState.inFreeSpins) return;

        const scatterCount = this.countScatters(symbolGrid);

        if (scatterCount >= this.freeSpinsConfig.triggerScatters) {
            this.triggerFreeSpins(scatterCount);
        }
    }

    /**
     * Check for free spins retrigger
     * @param {Array} symbolGrid - Symbol grid
     */
    checkFreeSpinsRetrigger(symbolGrid) {
        if (!this.gameState.inFreeSpins) return;

        const scatterCount = this.countScatters(symbolGrid);

        if (scatterCount >= this.freeSpinsConfig.retriggerScatters) {
            this.retriggerFreeSpins(scatterCount);
        }
    }

    /**
     * Count scatter symbols in grid
     * @param {Array} symbolGrid - Symbol grid
     * @returns {number} Number of scatters
     */
    countScatters(symbolGrid) {
        let count = 0;

        for (let reel = 0; reel < this.config.reels; reel++) {
            for (let row = 0; row < this.config.rows; row++) {
                if (symbolGrid[reel][row] === 'SCATTER') {
                    count++;
                }
            }
        }

        return count;
    }

    /**
     * Trigger free spins
     * @param {number} scatterCount - Number of triggering scatters
     */
    triggerFreeSpins(scatterCount) {
        const spinsAwarded = this.calculateFreeSpinsAwarded(scatterCount);

        this.gameState.inFreeSpins = true;
        this.gameState.freeSpinsRemaining = spinsAwarded;
        this.gameState.currentMultiplier = this.freeSpinsConfig.baseMultiplier;

        // Create bonus round data
        this.currentBonusRound = {
            type: 'free_spins',
            startTime: Date.now(),
            initialSpins: spinsAwarded,
            totalSpins: spinsAwarded,
            spinsPlayed: 0,
            totalWin: 0,
            biggestWin: 0,
            retriggers: 0,
            maxMultiplier: this.freeSpinsConfig.baseMultiplier
        };

        // Show free spins display
        this.scene.uiManager.showFreeSpinsDisplay(spinsAwarded);

        // Add to bonus history
        this.bonusHistory.push({
            type: 'free_spins_trigger',
            scatterCount: scatterCount,
            spinsAwarded: spinsAwarded,
            timestamp: Date.now()
        });

        // Trigger celebration
        this.scene.uiManager.showMessage(`FREE SPINS! ${spinsAwarded} spins awarded!`);
        this.playBonusSound('free_spins_trigger');
    }

    /**
     * Retrigger free spins
     * @param {number} scatterCount - Number of retriggering scatters
     */
    retriggerFreeSpins(scatterCount) {
        const additionalSpins = this.freeSpinsConfig.retriggerSpins;

        this.gameState.freeSpinsRemaining += additionalSpins;

        // Update bonus round data
        if (this.currentBonusRound) {
            this.currentBonusRound.totalSpins += additionalSpins;
            this.currentBonusRound.retriggers++;
        }

        // Update display
        this.scene.uiManager.updateFreeSpins();

        // Add to bonus history
        this.bonusHistory.push({
            type: 'free_spins_retrigger',
            scatterCount: scatterCount,
            spinsAwarded: additionalSpins,
            timestamp: Date.now()
        });

        // Trigger celebration
        this.scene.uiManager.showMessage(`RETRIGGER! +${additionalSpins} free spins!`);
        this.playBonusSound('free_spins_retrigger');
    }

    /**
     * Calculate free spins awarded based on scatter count
     * @param {number} scatterCount - Number of scatters
     * @returns {number} Number of free spins
     */
    calculateFreeSpinsAwarded(scatterCount) {
        const baseSpins = this.freeSpinsConfig.initialSpins;

        // Award extra spins for more scatters
        switch (scatterCount) {
            case 3: return baseSpins;
            case 4: return baseSpins + 5;
            case 5: return baseSpins + 15;
            default: return baseSpins;
        }
    }

    /**
     * Check for wild transformations
     * @param {Array} symbolGrid - Symbol grid
     */
    checkWildTransformations(symbolGrid) {
        if (!this.wildTransformConfig.transformOnNonWin) return;

        const transformations = [];
        let transformCount = 0;

        // Find all golden card symbols
        for (let reel = 0; reel < this.config.reels; reel++) {
            for (let row = 0; row < this.config.rows; row++) {
                if (symbolGrid[reel][row] === 'GOLDEN_CARD' &&
                    transformCount < this.wildTransformConfig.maxTransformsPerSpin) {

                    const transformation = this.attemptWildTransformation(reel, row);
                    if (transformation) {
                        transformations.push(transformation);
                        transformCount++;
                    }
                }
            }
        }

        // Apply transformations
        if (transformations.length > 0) {
            this.applyWildTransformations(symbolGrid, transformations);
        }
    }

    /**
     * Attempt wild transformation for a specific position
     * @param {number} reel - Reel index
     * @param {number} row - Row index
     * @returns {Object|null} Transformation data or null
     */
    attemptWildTransformation(reel, row) {
        const random = Math.random();

        if (random < this.wildTransformConfig.goldenToBigJoker) {
            return {
                reel: reel,
                row: row,
                fromSymbol: 'GOLDEN_CARD',
                toSymbol: 'BIG_JOKER',
                type: 'big_joker'
            };
        } else if (random < this.wildTransformConfig.goldenToBigJoker + this.wildTransformConfig.goldenToLittleJoker) {
            return {
                reel: reel,
                row: row,
                fromSymbol: 'GOLDEN_CARD',
                toSymbol: 'LITTLE_JOKER',
                type: 'little_joker'
            };
        }

        return null;
    }

    /**
     * Apply wild transformations to the grid
     * @param {Array} symbolGrid - Symbol grid
     * @param {Array} transformations - Array of transformations
     */
    applyWildTransformations(symbolGrid, transformations) {
        transformations.forEach(transform => {
            // Update symbol grid
            symbolGrid[transform.reel][transform.row] = transform.toSymbol;

            // Update sprite
            const sprite = this.scene.symbolSprites[transform.reel][transform.row];
            if (sprite) {
                // Animate transformation
                this.animateWildTransformation(sprite, transform);
            }

            // Add to bonus history
            this.bonusHistory.push({
                type: 'wild_transformation',
                transformation: transform,
                timestamp: Date.now()
            });
        });

        // Show transformation message
        if (transformations.length > 0) {
            const message = transformations.length === 1 ?
                'WILD TRANSFORMATION!' :
                `${transformations.length} WILD TRANSFORMATIONS!`;

            this.scene.uiManager.showMessage(message);
            this.playBonusSound('wild_transformation');
        }
    }

    /**
     * Animate wild transformation
     * @param {Phaser.GameObjects.Image} sprite - Symbol sprite
     * @param {Object} transform - Transformation data
     */
    animateWildTransformation(sprite, transform) {
        // Create transformation effect
        this.scene.tweens.add({
            targets: sprite,
            scaleX: 0,
            scaleY: 0,
            rotation: Math.PI * 2,
            duration: 300,
            ease: 'Power2.easeIn',
            onComplete: () => {
                // Change texture
                sprite.setTexture(transform.toSymbol);

                // Scale back up
                this.scene.tweens.add({
                    targets: sprite,
                    scaleX: 1.2,
                    scaleY: 1.2,
                    rotation: 0,
                    duration: 300,
                    ease: 'Back.easeOut',
                    onComplete: () => {
                        // Return to normal size
                        this.scene.tweens.add({
                            targets: sprite,
                            scaleX: 1,
                            scaleY: 1,
                            duration: 200,
                            ease: 'Power2.easeOut'
                        });
                    }
                });
            }
        });

        // Add particle effect
        this.createTransformationParticles(sprite.x, sprite.y, transform.type);
    }

    /**
     * Create particle effect for transformation
     * @param {number} x - X position
     * @param {number} y - Y position
     * @param {string} type - Transformation type
     */
    createTransformationParticles(x, y, type) {
        const color = type === 'big_joker' ? 0xFF1493 : 0xFF69B4;

        // Create simple particle effect
        for (let i = 0; i < 10; i++) {
            const particle = this.scene.add.circle(x, y, 3, color);

            this.scene.tweens.add({
                targets: particle,
                x: x + (Math.random() - 0.5) * 100,
                y: y + (Math.random() - 0.5) * 100,
                alpha: 0,
                scale: 0,
                duration: 500 + Math.random() * 300,
                ease: 'Power2.easeOut',
                onComplete: () => particle.destroy()
            });
        }
    }

    /**
     * Update multiplier during free spins
     * @param {number} cascadeCount - Current cascade count
     */
    updateFreeSpinsMultiplier(cascadeCount) {
        if (!this.gameState.inFreeSpins) return;

        const newMultiplier = Math.min(
            this.freeSpinsConfig.maxMultiplier,
            this.freeSpinsConfig.baseMultiplier + (cascadeCount - 1) * this.freeSpinsConfig.multiplierIncrement
        );

        this.gameState.currentMultiplier = newMultiplier;

        // Update bonus round tracking
        if (this.currentBonusRound && newMultiplier > this.currentBonusRound.maxMultiplier) {
            this.currentBonusRound.maxMultiplier = newMultiplier;
        }

        // Update display
        this.scene.uiManager.updateMultiplier();
    }

    /**
     * End free spins bonus round
     */
    endFreeSpins() {
        if (!this.gameState.inFreeSpins) return;

        // Finalize bonus round data
        if (this.currentBonusRound) {
            this.currentBonusRound.endTime = Date.now();
            this.currentBonusRound.duration = this.currentBonusRound.endTime - this.currentBonusRound.startTime;

            // Add to bonus history
            this.bonusHistory.push({
                type: 'free_spins_complete',
                bonusRound: { ...this.currentBonusRound },
                timestamp: Date.now()
            });
        }

        // Reset game state
        this.gameState.inFreeSpins = false;
        this.gameState.freeSpinsRemaining = 0;
        this.gameState.currentMultiplier = 1;

        // Hide displays
        this.scene.uiManager.hideFreeSpinsDisplay();
        this.scene.uiManager.hideMultiplierDisplay();

        // Show completion message
        if (this.currentBonusRound && this.currentBonusRound.totalWin > 0) {
            this.scene.uiManager.showMessage(`Free Spins Complete! Won $${this.currentBonusRound.totalWin.toFixed(2)}`);
        }

        this.currentBonusRound = null;
        this.playBonusSound('free_spins_complete');
    }

    /**
     * Add win to current bonus round
     * @param {number} winAmount - Win amount
     */
    addBonusWin(winAmount) {
        if (this.currentBonusRound) {
            this.currentBonusRound.totalWin += winAmount;

            if (winAmount > this.currentBonusRound.biggestWin) {
                this.currentBonusRound.biggestWin = winAmount;
            }
        }
    }

    /**
     * Increment spins played in bonus round
     */
    incrementBonusSpinsPlayed() {
        if (this.currentBonusRound) {
            this.currentBonusRound.spinsPlayed++;
        }
    }

    /**
     * Play bonus sound effect
     * @param {string} soundType - Type of sound to play
     */
    playBonusSound(soundType) {
        // Placeholder for sound effects
        // Could implement actual sound playing here
        console.log(`Playing bonus sound: ${soundType}`);
    }

    /**
     * Get bonus statistics
     * @returns {Object} Bonus statistics
     */
    getBonusStatistics() {
        const stats = {
            totalBonusRounds: 0,
            totalFreeSpins: 0,
            totalBonusWins: 0,
            averageBonusWin: 0,
            biggestBonusWin: 0,
            wildTransformations: 0
        };

        this.bonusHistory.forEach(entry => {
            switch (entry.type) {
                case 'free_spins_complete':
                    stats.totalBonusRounds++;
                    stats.totalFreeSpins += entry.bonusRound.totalSpins;
                    stats.totalBonusWins += entry.bonusRound.totalWin;
                    if (entry.bonusRound.totalWin > stats.biggestBonusWin) {
                        stats.biggestBonusWin = entry.bonusRound.totalWin;
                    }
                    break;
                case 'wild_transformation':
                    stats.wildTransformations++;
                    break;
            }
        });

        if (stats.totalBonusRounds > 0) {
            stats.averageBonusWin = stats.totalBonusWins / stats.totalBonusRounds;
        }

        return stats;
    }

    /**
     * Clear bonus history
     */
    clearBonusHistory() {
        this.bonusHistory = [];
        this.currentBonusRound = null;
    }
}
