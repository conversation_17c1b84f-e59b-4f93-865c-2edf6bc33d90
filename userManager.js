/**
 * 👤 VEGAS ACE SLOTS - USER MANAGEMENT SYSTEM 👤
 * Professional user authentication and session management
 * Integrates with VegasAceDatabase for secure data storage
 */

class UserManager {
    constructor() {
        this.database = null;
        this.currentUser = null;
        this.currentSession = null;
        this.isLoggedIn = false;
        this.loginCallbacks = [];
        this.logoutCallbacks = [];
        
        console.log('👤 UserManager initialized');
    }

    /**
     * 🚀 Initialize user management system
     */
    async init() {
        try {
            // Initialize database
            this.database = new VegasAceDatabase();
            const dbInitialized = await this.database.init();
            
            if (!dbInitialized) {
                throw new Error('Database initialization failed');
            }

            // Setup event listeners
            this.setupEventListeners();
            
            // Check for existing session
            await this.checkExistingSession();
            
            console.log('✅ UserManager initialized successfully');
            return true;
            
        } catch (error) {
            console.error('❌ UserManager initialization failed:', error);
            return false;
        }
    }

    /**
     * 🎮 Setup authentication event listeners
     */
    setupEventListeners() {
        // Login form
        document.getElementById('login-btn')?.addEventListener('click', () => {
            this.handleLogin();
        });

        // Register form
        document.getElementById('register-btn')?.addEventListener('click', () => {
            this.handleRegister();
        });

        // Form toggles
        document.getElementById('show-register')?.addEventListener('click', () => {
            this.showRegisterForm();
        });

        document.getElementById('show-login')?.addEventListener('click', () => {
            this.showLoginForm();
        });

        // Profile management
        document.getElementById('profile-btn')?.addEventListener('click', () => {
            this.showProfile();
        });

        document.getElementById('logout-btn')?.addEventListener('click', () => {
            this.handleLogout();
        });

        // Enter key support
        document.getElementById('login-password')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.handleLogin();
        });

        document.getElementById('register-confirm')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.handleRegister();
        });

        console.log('🎮 Event listeners setup complete');
    }

    /**
     * 🔐 Handle user registration
     */
    async handleRegister() {
        try {
            const name = document.getElementById('register-name')?.value.trim();
            const mobile = document.getElementById('register-mobile')?.value.trim();
            const email = document.getElementById('register-email')?.value.trim() || '';
            const password = document.getElementById('register-password')?.value;
            const confirmPassword = document.getElementById('register-confirm')?.value;

            // Validation
            if (!name || !mobile || !password || !confirmPassword) {
                this.showMessage('❌ Please fill in all required fields!', 'error');
                return;
            }

            if (password.length < 6) {
                this.showMessage('❌ Password must be at least 6 characters!', 'error');
                return;
            }

            if (password !== confirmPassword) {
                this.showMessage('❌ Passwords do not match!', 'error');
                return;
            }

            if (!this.validateMobile(mobile)) {
                this.showMessage('❌ Please enter a valid mobile number!', 'error');
                return;
            }

            // Check if user already exists
            const existingUser = await this.database.findUserByMobile(mobile);
            if (existingUser) {
                this.showMessage('❌ User with this mobile number already exists!', 'error');
                return;
            }

            // Create new user
            const userData = { name, mobile, email, password };
            const newUser = await this.database.createUser(userData);
            
            this.showMessage('🎊 Account created successfully! Welcome to Vegas Ace Slots!', 'success');
            
            // Auto-login the new user
            setTimeout(async () => {
                await this.loginUser(newUser);
            }, 2000);

            console.log('✅ User registered successfully:', newUser.id);

        } catch (error) {
            console.error('❌ Registration error:', error);
            this.showMessage('❌ Registration failed. Please try again.', 'error');
        }
    }

    /**
     * 🔑 Handle user login
     */
    async handleLogin() {
        try {
            const mobile = document.getElementById('login-mobile')?.value.trim();
            const password = document.getElementById('login-password')?.value;

            if (!mobile || !password) {
                this.showMessage('❌ Please enter mobile number and password!', 'error');
                return;
            }

            const authResult = await this.database.authenticateUser(mobile, password);
            
            if (!authResult) {
                this.showMessage('❌ Invalid mobile number or password!', 'error');
                return;
            }

            if (authResult.error === 'unauthorized_device') {
                this.handleUnauthorizedDevice(authResult.user);
                return;
            }

            this.showMessage('🎯 Login successful! Welcome back!', 'success');
            
            setTimeout(async () => {
                await this.loginUser(authResult);
            }, 1500);

            console.log('✅ User logged in successfully:', authResult.id);

        } catch (error) {
            console.error('❌ Login error:', error);
            this.showMessage('❌ Login failed. Please try again.', 'error');
        }
    }

    /**
     * 🚫 Handle unauthorized device access
     */
    handleUnauthorizedDevice(user) {
        const deviceId = this.database.generateDeviceFingerprint();
        
        this.showMessage(`❌ Unauthorized device detected! For security, this account can only be accessed from registered devices.`, 'error');
        
        // Could implement device authorization flow here
        console.log('🚫 Unauthorized device access attempt:', user.mobile, deviceId);
    }

    /**
     * 👤 Login user and setup session
     */
    async loginUser(user) {
        try {
            this.currentUser = user;
            this.isLoggedIn = true;
            
            // Create session
            const deviceId = this.database.generateDeviceFingerprint();
            this.currentSession = await this.database.createSession(user.id, deviceId);
            
            // Update game state
            if (typeof gameState !== 'undefined') {
                gameState.balance = user.balance;
            }
            
            // Show game interface
            this.showGameInterface();
            
            // Trigger login callbacks
            this.loginCallbacks.forEach(callback => callback(user));
            
            console.log('✅ User session established:', user.id);

        } catch (error) {
            console.error('❌ Error establishing user session:', error);
            this.showMessage('❌ Failed to establish session. Please try again.', 'error');
        }
    }

    /**
     * 🚪 Handle user logout
     */
    async handleLogout() {
        if (!confirm('Are you sure you want to logout?')) {
            return;
        }

        try {
            // Save current game state
            if (this.currentUser && typeof gameState !== 'undefined') {
                await this.database.updateUserBalance(this.currentUser.id, gameState.balance);
            }

            // Clear session
            this.currentUser = null;
            this.currentSession = null;
            this.isLoggedIn = false;

            // Trigger logout callbacks
            this.logoutCallbacks.forEach(callback => callback());

            this.showMessage('👋 Logged out successfully!', 'info');
            
            setTimeout(() => {
                this.showAuthInterface();
                this.clearForms();
            }, 1500);

            console.log('✅ User logged out successfully');

        } catch (error) {
            console.error('❌ Logout error:', error);
            this.showMessage('❌ Logout failed. Please try again.', 'error');
        }
    }

    /**
     * 🔍 Check for existing session
     */
    async checkExistingSession() {
        // For now, always show auth interface
        // In a real implementation, you'd check for valid session tokens
        this.showAuthInterface();
        console.log('🔍 No existing session found');
    }

    /**
     * 🎮 Show game interface
     */
    showGameInterface() {
        document.getElementById('auth-modal').style.display = 'none';
        document.getElementById('game-container').style.display = 'block';
        this.updateUserDisplay();
        
        // Initialize game if function exists
        if (typeof initGame === 'function') {
            initGame();
        }
    }

    /**
     * 🔐 Show authentication interface
     */
    showAuthInterface() {
        document.getElementById('auth-modal').style.display = 'flex';
        document.getElementById('game-container').style.display = 'none';
    }

    /**
     * 📝 Show register form
     */
    showRegisterForm() {
        document.getElementById('login-form').style.display = 'none';
        document.getElementById('register-form').style.display = 'block';
    }

    /**
     * 📝 Show login form
     */
    showLoginForm() {
        document.getElementById('register-form').style.display = 'none';
        document.getElementById('login-form').style.display = 'block';
    }

    /**
     * 🧹 Clear forms
     */
    clearForms() {
        const forms = ['login-form', 'register-form'];
        forms.forEach(formId => {
            const form = document.getElementById(formId);
            if (form) {
                const inputs = form.querySelectorAll('input');
                inputs.forEach(input => input.value = '');
            }
        });
    }

    /**
     * 📱 Validate mobile number
     */
    validateMobile(mobile) {
        const mobileRegex = /^[+]?[\d\s\-\(\)]{10,15}$/;
        return mobileRegex.test(mobile);
    }

    /**
     * 📊 Update user display
     */
    updateUserDisplay() {
        if (!this.currentUser) return;

        // Update balance display
        const balanceElement = document.getElementById('balance');
        if (balanceElement) {
            balanceElement.textContent = this.currentUser.balance.toFixed(2);
        }

        // Update user info display
        const userNameElement = document.getElementById('user-name');
        if (userNameElement) {
            userNameElement.textContent = this.currentUser.name;
        }
    }

    /**
     * 💰 Update user balance
     */
    async updateBalance(newBalance) {
        if (!this.currentUser) return false;

        try {
            this.currentUser.balance = newBalance;
            await this.database.updateUserBalance(this.currentUser.id, newBalance);
            this.updateUserDisplay();
            return true;
        } catch (error) {
            console.error('❌ Error updating balance:', error);
            return false;
        }
    }

    /**
     * 📝 Show message to user
     */
    showMessage(text, type = 'info') {
        // Create or update message element
        let messageEl = document.getElementById('auth-message');
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.id = 'auth-message';
            messageEl.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                padding: 15px 25px;
                border-radius: 8px;
                font-weight: bold;
                z-index: 9999;
                max-width: 400px;
                text-align: center;
            `;
            document.body.appendChild(messageEl);
        }

        // Set message style based on type
        const styles = {
            success: 'background: #28a745; color: white; border: 2px solid #1e7e34;',
            error: 'background: #dc3545; color: white; border: 2px solid #c82333;',
            info: 'background: #17a2b8; color: white; border: 2px solid #138496;'
        };

        messageEl.style.cssText += styles[type] || styles.info;
        messageEl.textContent = text;
        messageEl.style.display = 'block';

        // Auto-hide after 4 seconds
        setTimeout(() => {
            messageEl.style.display = 'none';
        }, 4000);
    }

    /**
     * 🔗 Add login callback
     */
    onLogin(callback) {
        this.loginCallbacks.push(callback);
    }

    /**
     * 🔗 Add logout callback
     */
    onLogout(callback) {
        this.logoutCallbacks.push(callback);
    }
}

// 🌟 Export the UserManager class
window.UserManager = UserManager;

console.log('👤 UserManager System Loaded');
