/* Vegas Ace Slots - Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #fff;
    overflow: hidden;
    height: 100vh;
}

#game-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
}

/* Header Styles */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background: rgba(0, 0, 0, 0.3);
    border-bottom: 2px solid #ffd700;
}

.game-title {
    font-size: 1.8rem;
    font-weight: 900;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.game-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 80px;
}

.stat-label {
    font-size: 0.7rem;
    color: #ccc;
    margin-bottom: 2px;
}

.stat-value {
    font-size: 1rem;
    font-weight: 700;
    color: #ffd700;
}

/* Game Canvas */
#phaser-game {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    background: radial-gradient(circle at center, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
}

/* Game Controls */
.game-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.4);
    border-top: 2px solid #ffd700;
}

.bet-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.control-btn {
    width: 40px;
    height: 40px;
    border: 2px solid #ffd700;
    background: linear-gradient(145deg, #2a2a3e, #1a1a2e);
    color: #ffd700;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.control-btn:hover {
    background: linear-gradient(145deg, #3a3a4e, #2a2a3e);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.control-btn:active {
    transform: scale(0.95);
}

.bet-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 10px;
}

.bet-label {
    font-size: 0.7rem;
    color: #ccc;
}

.bet-value {
    font-size: 1rem;
    font-weight: 700;
    color: #ffd700;
}

.spin-button {
    width: 120px;
    height: 60px;
    border: 3px solid #ffd700;
    background: linear-gradient(145deg, #ff6b35, #ff4500);
    color: #fff;
    font-family: 'Orbitron', monospace;
    font-size: 1.2rem;
    font-weight: 900;
    cursor: pointer;
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
}

.spin-button:hover {
    background: linear-gradient(145deg, #ff7b45, #ff5500);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.6);
    transform: translateY(-2px);
}

.spin-button:active {
    transform: translateY(0);
}

.spin-button:disabled {
    background: linear-gradient(145deg, #666, #444);
    cursor: not-allowed;
    box-shadow: none;
}

.max-bet {
    width: 80px;
    height: 40px;
    font-size: 0.8rem;
}

/* Multiplier Display */
.multiplier-display {
    position: absolute;
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid #ffd700;
    border-radius: 10px;
    padding: 10px 15px;
    text-align: center;
    animation: pulse 2s infinite;
}

.multiplier-text {
    display: block;
    font-size: 0.8rem;
    color: #ccc;
    margin-bottom: 5px;
}

.multiplier-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 900;
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

/* Free Spins Display */
.freespins-display {
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    background: rgba(138, 43, 226, 0.9);
    border: 2px solid #dda0dd;
    border-radius: 10px;
    padding: 10px 15px;
    text-align: center;
    animation: glow 2s infinite alternate;
}

.freespins-text {
    display: block;
    font-size: 0.8rem;
    color: #fff;
    margin-bottom: 5px;
}

.freespins-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 900;
    color: #dda0dd;
    text-shadow: 0 0 10px rgba(221, 160, 221, 0.5);
}

/* Win Celebration */
.win-celebration {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 1000;
    animation: winCelebration 3s ease-out;
}

.win-amount-big {
    font-size: 3rem;
    font-weight: 900;
    color: #ffd700;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
    margin-bottom: 10px;
}

.win-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ff6b35;
    text-shadow: 0 0 15px rgba(255, 107, 53, 0.6);
}

/* Loading Screen */
.loading-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.loading-content {
    text-align: center;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 215, 0, 0.3);
    border-top: 4px solid #ffd700;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

.loading-text {
    font-size: 1.2rem;
    color: #ffd700;
    font-weight: 700;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: translateY(-50%) scale(1); }
    50% { transform: translateY(-50%) scale(1.05); }
}

@keyframes glow {
    0% { box-shadow: 0 0 10px rgba(221, 160, 221, 0.5); }
    100% { box-shadow: 0 0 20px rgba(221, 160, 221, 0.8); }
}

@keyframes winCelebration {
    0% { transform: translate(-50%, -50%) scale(0); opacity: 0; }
    20% { transform: translate(-50%, -50%) scale(1.2); opacity: 1; }
    80% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
    100% { transform: translate(-50%, -50%) scale(1); opacity: 0; }
}

@keyframes messageSlideIn {
    0% { transform: translate(-50%, -50%) translateY(-20px); opacity: 0; }
    100% { transform: translate(-50%, -50%) translateY(0); opacity: 1; }
}

@keyframes messageSlideOut {
    0% { transform: translate(-50%, -50%) translateY(0); opacity: 1; }
    100% { transform: translate(-50%, -50%) translateY(20px); opacity: 0; }
}

@keyframes valueChange {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); color: #ff6b35; }
    100% { transform: scale(1); }
}

/* Additional UI States */
.value-changing {
    animation: valueChange 0.3s ease-out;
}

.spinning .spin-text {
    animation: spin 1s linear infinite;
}

.insufficient-funds {
    opacity: 0.5;
    cursor: not-allowed !important;
}

.high-multiplier {
    animation: pulse 1s infinite, glow 2s infinite alternate;
}

.mega {
    color: #ff1493 !important;
    text-shadow: 0 0 30px rgba(255, 20, 147, 0.8) !important;
}

.super {
    color: #ff6b35 !important;
    text-shadow: 0 0 25px rgba(255, 107, 53, 0.8) !important;
}

.big {
    color: #ffd700 !important;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.8) !important;
}

/* Mobile-specific adjustments */
.mobile-layout .multiplier-display,
.mobile-layout .freespins-display {
    position: relative;
    transform: none;
    margin: 5px;
    display: inline-block;
}

/* Animation pause state */
.animations-paused * {
    animation-play-state: paused !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-title {
        font-size: 1.4rem;
    }

    .game-stats {
        gap: 10px;
    }

    .stat-item {
        min-width: 60px;
    }

    .stat-label {
        font-size: 0.6rem;
    }

    .stat-value {
        font-size: 0.9rem;
    }

    .game-controls {
        padding: 10px 15px;
    }

    .control-btn {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .spin-button {
        width: 100px;
        height: 50px;
        font-size: 1rem;
    }

    .multiplier-display,
    .freespins-display {
        position: static;
        transform: none;
        margin: 10px;
        display: inline-block;
    }

    .win-amount-big {
        font-size: 2rem;
    }

    .win-text {
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .game-header {
        flex-direction: column;
        gap: 10px;
        padding: 10px;
    }

    .game-title {
        font-size: 1.2rem;
    }

    .game-stats {
        gap: 15px;
    }

    .game-controls {
        flex-direction: column;
        gap: 15px;
        padding: 15px;
    }

    .bet-controls {
        order: 2;
    }

    .spin-button {
        order: 1;
        width: 120px;
        height: 60px;
    }

    .max-bet {
        order: 3;
        width: 100px;
    }
}
